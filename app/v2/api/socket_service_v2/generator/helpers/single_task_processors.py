"""
Single Task Processors for Task Utils V2
Handles processing of individual tasks by specific type.
"""

from typing import Dict, Any
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

# Import helper functions
from .options_audio_generator import generate_options_audio
from .logging_helper import get_current_timestamp

logger = setup_new_logging(__name__)


async def process_single_choice_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server=None
):
    """
    Process single choice task completely with options audio.
    
    Args:
        current_user: Current user context
        task: Task dictionary to process
        socketio_server: WebSocket server for notifications
    """
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎵 Processing single choice task {task_id} with options audio")
    
    try:
        # Generate options audio sequentially with 2-second delays
        if task.get("question", {}).get("options"):
            from bson.objectid import ObjectId
            if isinstance(task_id, str):
                task_id = ObjectId(task_id) if ObjectId.is_valid(task_id) else ObjectId()
            await generate_options_audio(current_user, task, task_id, socketio_server)
        
        # Mark task as completed
        task["status"] = "completed"
        task["completed_at"] = get_current_timestamp()
        
        logger.info(f"✅ Single choice task {task_id} completed with options audio")
        
    except Exception as e:
        logger.error(f"❌ Failed to process single choice task {task_id}: {e}")
        task["status"] = "failed"
        task["error"] = str(e)
        raise


async def process_multiple_choice_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server=None
):
    """
    Process multiple choice task completely with options audio.
    
    Args:
        current_user: Current user context
        task: Task dictionary to process
        socketio_server: WebSocket server for notifications
    """
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎵 Processing multiple choice task {task_id} with options audio")
    
    try:
        # Generate options audio sequentially with 2-second delays
        if task.get("question", {}).get("options"):
            from bson.objectid import ObjectId
            if isinstance(task_id, str):
                task_id = ObjectId(task_id) if ObjectId.is_valid(task_id) else ObjectId()
            await generate_options_audio(current_user, task, task_id, socketio_server)
        
        # Mark task as completed
        task["status"] = "completed"
        task["completed_at"] = get_current_timestamp()
        
        logger.info(f"✅ Multiple choice task {task_id} completed with options audio")
        
    except Exception as e:
        logger.error(f"❌ Failed to process multiple choice task {task_id}: {e}")
        task["status"] = "failed"
        task["error"] = str(e)
        raise


async def process_image_audio_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server=None
):
    """
    Process image/audio task completely with media and options audio.
    
    Args:
        current_user: Current user context
        task: Task dictionary to process
        socketio_server: WebSocket server for notifications
    """
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎨🎵 Processing image/audio task {task_id} with media and options audio")
    
    try:
        # Generate task media (image/audio) first
        # This would call the appropriate media generation functions
        # await generate_task_media_independent(current_user, task, socketio_server)
        logger.info(f"🎨 Media generation for task {task_id} (placeholder - implement media generation)")
        
        # Generate options audio sequentially
        if task.get("question", {}).get("options"):
            from bson.objectid import ObjectId
            if isinstance(task_id, str):
                task_id = ObjectId(task_id) if ObjectId.is_valid(task_id) else ObjectId()
            await generate_options_audio(current_user, task, task_id, socketio_server)
        
        # Mark task as completed
        task["status"] = "completed"
        task["completed_at"] = get_current_timestamp()
        
        logger.info(f"✅ Image/audio task {task_id} completed with media and options audio")
        
    except Exception as e:
        logger.error(f"❌ Failed to process image/audio task {task_id}: {e}")
        task["status"] = "failed"
        task["error"] = str(e)
        raise


async def process_story_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server=None
):
    """
    Process story task completely.
    
    Args:
        current_user: Current user context
        task: Task dictionary to process
        socketio_server: WebSocket server for notifications
    """
    task_id = task.get("id") or task.get("_id")
    logger.info(f"📚 Processing story task {task_id}")
    
    try:
        # Process story task
        # This would call the appropriate story processing functions
        logger.info(f"📚 Story processing for task {task_id} (placeholder - implement story processing)")
        
        # Mark task as completed
        task["status"] = "completed"
        task["completed_at"] = get_current_timestamp()
        
        logger.info(f"✅ Story task {task_id} completed")
        
    except Exception as e:
        logger.error(f"❌ Failed to process story task {task_id}: {e}")
        task["status"] = "failed"
        task["error"] = str(e)
        raise
