"""
Story Media Generator for Task Utils V2
Handles image and audio generation for story steps.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from bson.objectid import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.database import retry_db_operation
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio
from app.v2.api.socket_service_v2.generator.imagen import generate_image

from .media_cache_helper import check_media_cache, save_media_cache
from .core_utils import serialize_usage_metadata

logger = setup_new_logging(__name__)


async def generate_story_media_independent(
    current_user: UserTenantDB,
    story_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate image and audio for a single story independently, storing each as soon as generated."""
    try:
        story_id = story_item["_id"]
        stage = story_item["stage"]
        image_description = story_item.get("image", "")
        script_text = story_item.get("script", "")

        logger.info(f"🖼️ PRIORITY 2: Generating media for story {story_id} stage {stage}")

        # Start image and audio generation independently - don't wait for each other
        image_task = None
        audio_task = None

        # Start image generation if needed
        if image_description:
            image_task = asyncio.create_task(generate_and_store_story_image(
                current_user, story_id, stage, image_description, socketio_server
            ))
            image_task.add_done_callback(
                lambda t: logger.error(f"❌ Story image generation failed for {story_id}: {t.exception()}")
                if t.exception() else logger.debug(f"✅ Story image generation completed for {story_id}")
            )
        else:
            # No image description - mark as ready immediately
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata._image_ready": True,
                        "metadata._priority": "no_image_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story {story_id} image marked as ready (no description provided)")

        # Start audio generation if needed
        if script_text:
            audio_task = asyncio.create_task(generate_and_store_story_audio(
                current_user, story_id, stage, script_text, socketio_server
            ))
            audio_task.add_done_callback(
                lambda t: logger.error(f"❌ Story audio generation failed for {story_id}: {t.exception()}")
                if t.exception() else logger.debug(f"✅ Story audio generation completed for {story_id}")
            )
        else:
            # No script text - mark as ready immediately
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata._audio_ready": True,
                        "metadata._priority": "no_audio_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story {story_id} audio marked as ready (no script provided)")

        # Wait for both tasks to complete if they were started
        if image_task or audio_task:
            tasks_to_wait = [task for task in [image_task, audio_task] if task is not None]
            if tasks_to_wait:
                await asyncio.gather(*tasks_to_wait, return_exceptions=True)

        logger.info(f"✅ Story {story_id} stage {stage} media generation completed")

    except Exception as e:
        logger.error(f"❌ Failed to start media generation for story {story_item.get('_id')}: {e}")


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def generate_and_store_story_image(
    current_user: UserTenantDB,
    story_id: ObjectId,
    stage: int,
    image_description: str,
    socketio_server: Optional[Any] = None
):
    """Generate and store story image."""
    try:
        logger.info(f"🖼️ STORY {story_id} STAGE {stage} | IMAGE: PROCESSING | DESC: {image_description[:50]}...")

        # Check cache first
        cached_image = await check_media_cache(current_user, image_description, "image", "story_image")

        if cached_image:
            # Use cached image
            file_info = cached_image["file_info"]
            usage_metadata = cached_image["usage_metadata"]

            # Store cached image metadata
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "image_metadata": file_info,
                        "metadata._image_ready": True,
                        "metadata._cached": True,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )
            logger.info(f"🎯 STORY {story_id} STAGE {stage} | IMAGE: CACHED | URL: {file_info.get('url')}")
            return

        # Generate new image
        logger.info(f"🎨 STORY {story_id} STAGE {stage} | IMAGE: GENERATING")
        _file_text, file_info, usage_metadata = await generate_image(current_user, image_description)

        if file_info:
            # Store image metadata immediately
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "image_metadata": file_info,
                        "metadata._image_ready": True,
                        "metadata._cached": False,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )

            # Save to cache for future use
            await save_media_cache(
                current_user, image_description, "image", "story_image",
                _file_text or "", file_info, usage_metadata
            )

            logger.info(f"✅ STORY {story_id} STAGE {stage} | IMAGE: GENERATED | URL: {file_info.get('url')}")
        else:
            logger.error(f"❌ STORY {story_id} STAGE {stage} | IMAGE: FAILED | No image data received")
            await _mark_story_media_failed(current_user, story_id, "image", "No image data received")

    except Exception as e:
        logger.error(f"❌ Failed to generate image for story {story_id} stage {stage}: {e}")
        await _mark_story_media_failed(current_user, story_id, "image", str(e))


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def generate_and_store_story_audio(
    current_user: UserTenantDB,
    story_id: ObjectId,
    stage: int,
    script_text: str,
    socketio_server: Optional[Any] = None
):
    """Generate and store story audio."""
    try:
        logger.info(f"🔊 STORY {story_id} STAGE {stage} | AUDIO: PROCESSING | TEXT: {script_text[:50]}...")

        # Check cache first
        cached_audio = await check_media_cache(current_user, script_text, "audio", "audio_prompt")

        if cached_audio:
            # Use cached audio
            file_info = cached_audio["file_info"]
            usage_metadata = cached_audio["usage_metadata"]

            # Store cached audio metadata
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata": file_info,
                        "metadata._audio_ready": True,
                        "metadata._cached": True,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )
            logger.info(f"🎯 STORY {story_id} STAGE {stage} | AUDIO: CACHED | URL: {file_info.get('url')}")
            return

        # Generate new audio
        logger.info(f"🎵 STORY {story_id} STAGE {stage} | AUDIO: GENERATING")
        _file_text, file_info, usage_metadata = await generate_audio(current_user, script_text, "audio_prompt")

        if file_info:
            # Store audio metadata immediately
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata": file_info,
                        "metadata._audio_ready": True,
                        "metadata._cached": False,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )

            # Save to cache for future use
            await save_media_cache(
                current_user, script_text, "audio", "audio_prompt",
                _file_text or "", file_info, usage_metadata
            )

            logger.info(f"✅ STORY {story_id} STAGE {stage} | AUDIO: GENERATED | URL: {file_info.get('url')}")
        else:
            logger.error(f"❌ STORY {story_id} STAGE {stage} | AUDIO: FAILED | No audio data received")
            await _mark_story_media_failed(current_user, story_id, "audio", "No audio data received")

    except Exception as e:
        logger.error(f"❌ Failed to generate audio for story {story_id} stage {stage}: {e}")
        await _mark_story_media_failed(current_user, story_id, "audio", str(e))


async def _mark_story_media_failed(current_user: UserTenantDB, story_id: ObjectId, media_type: str, error_msg: str):
    """Mark story media generation as failed."""
    try:
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    f"metadata._{media_type}_ready": False,
                    f"metadata._{media_type}_error": error_msg,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        logger.error(f"❌ Marked story {story_id} {media_type} as failed: {error_msg}")
    except Exception as db_error:
        logger.error(f"❌ Failed to mark story {story_id} {media_type} as failed: {db_error}")
