"""
Utility Helper for Task Utils V2
Provides common utility functions and retry logic.
"""

import asyncio
import random
from datetime import datetime, timezone
from typing import Dict, Any, Callable, Optional, Union
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


def map_task_type(task_type: str) -> str:
    """
    Map task type to standardized format.
    
    Args:
        task_type: Original task type
    
    Returns:
        Standardized task type
    """
    task_type_mapping = {
        "single_choice": "single_choice_task",
        "multiple_choice": "multiple_choice_task",
        "image_audio": "image_audio_task",
        "audio": "audio_task",
        "image": "image_task",
        "story": "story_task"
    }
    
    return task_type_mapping.get(task_type, task_type)


def serialize_usage_metadata(usage_metadata: Any) -> Dict[str, Any]:
    """
    Serialize usage metadata to dictionary format.
    
    Args:
        usage_metadata: Usage metadata object
    
    Returns:
        Serialized metadata dictionary
    """
    try:
        if hasattr(usage_metadata, 'model_dump'):
            return usage_metadata.model_dump()
        elif hasattr(usage_metadata, 'dict'):
            return usage_metadata.dict()
        elif isinstance(usage_metadata, dict):
            return usage_metadata
        else:
            return {"raw_metadata": str(usage_metadata)}
    except Exception as e:
        logger.warning(f"⚠️ Error serializing usage metadata: {e}")
        return {"error": "serialization_failed", "raw_metadata": str(usage_metadata)}


async def retry_with_exponential_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    *args,
    **kwargs
) -> Any:
    """
    Retry a function with exponential backoff.
    
    Args:
        func: Function to retry
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        backoff_factor: Backoff multiplication factor
        jitter: Whether to add random jitter to delay
        *args: Function arguments
        **kwargs: Function keyword arguments
    
    Returns:
        Function result if successful
    
    Raises:
        Last exception if all retries failed
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            if attempt > 0:
                logger.info(f"✅ RETRY SUCCESS | ATTEMPT: {attempt + 1}/{max_retries + 1}")
            
            return result
            
        except Exception as e:
            last_exception = e
            
            if attempt == max_retries:
                logger.error(f"❌ RETRY FAILED | ALL ATTEMPTS EXHAUSTED | ERROR: {e}")
                break
            
            # Calculate delay with exponential backoff
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            
            # Add jitter to prevent thundering herd
            if jitter:
                delay = delay * (0.5 + random.random() * 0.5)
            
            logger.warning(f"⚠️ RETRY ATTEMPT {attempt + 1}/{max_retries + 1} | DELAY: {delay:.1f}s | ERROR: {e}")
            await asyncio.sleep(delay)
    
    raise last_exception


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable format.
    
    Args:
        seconds: Duration in seconds
    
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def calculate_success_rate(successful: int, total: int) -> str:
    """
    Calculate success rate percentage.
    
    Args:
        successful: Number of successful operations
        total: Total number of operations
    
    Returns:
        Success rate as percentage string
    """
    if total == 0:
        return "0%"
    
    rate = (successful / total) * 100
    return f"{rate:.1f}%"


def get_current_timestamp() -> datetime:
    """Get current UTC timestamp."""
    return datetime.now(timezone.utc)


def format_timestamp(timestamp: datetime) -> str:
    """Format timestamp to ISO string."""
    return timestamp.isoformat()


def validate_task_data(task_data: Dict[str, Any]) -> bool:
    """
    Validate task data structure.
    
    Args:
        task_data: Task data to validate
    
    Returns:
        True if valid, False otherwise
    """
    required_fields = ["_id", "task_type"]
    
    for field in required_fields:
        if field not in task_data:
            logger.error(f"❌ VALIDATION FAILED | MISSING FIELD: {field}")
            return False
    
    return True


def validate_story_data(story_data: Dict[str, Any]) -> bool:
    """
    Validate story data structure.
    
    Args:
        story_data: Story data to validate
    
    Returns:
        True if valid, False otherwise
    """
    required_fields = ["story_id", "story_steps"]
    
    for field in required_fields:
        if field not in story_data:
            logger.error(f"❌ STORY VALIDATION FAILED | MISSING FIELD: {field}")
            return False
    
    return True


def extract_keywords_from_text(text: str, max_keywords: int = 5) -> list:
    """
    Extract keywords from text for caching purposes.
    
    Args:
        text: Text to extract keywords from
        max_keywords: Maximum number of keywords to extract
    
    Returns:
        List of extracted keywords
    """
    try:
        # Simple keyword extraction (can be enhanced with NLP)
        words = text.lower().split()
        
        # Remove common stop words (basic list)
        stop_words = {"को", "का", "की", "है", "हैं", "में", "से", "पर", "और", "या", "the", "a", "an", "is", "are", "in", "on", "at", "to", "for"}
        
        # Filter words
        keywords = [word.strip("।?!.,") for word in words if len(word) > 2 and word not in stop_words]
        
        # Return unique keywords up to max limit
        unique_keywords = list(dict.fromkeys(keywords))  # Preserve order while removing duplicates
        return unique_keywords[:max_keywords]
        
    except Exception as e:
        logger.warning(f"⚠️ Error extracting keywords from text: {e}")
        return []


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage.
    
    Args:
        filename: Original filename
    
    Returns:
        Sanitized filename
    """
    import re
    
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Trim underscores from start and end
    sanitized = sanitized.strip('_')
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = "unnamed_file"
    
    return sanitized


def generate_unique_id() -> str:
    """Generate a unique ID for tracking purposes."""
    import uuid
    return str(uuid.uuid4())


def chunk_list(items: list, chunk_size: int) -> list:
    """
    Split a list into chunks of specified size.
    
    Args:
        items: List to chunk
        chunk_size: Size of each chunk
    
    Returns:
        List of chunks
    """
    chunks = []
    for i in range(0, len(items), chunk_size):
        chunks.append(items[i:i + chunk_size])
    return chunks


def merge_dictionaries(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge multiple dictionaries, with later ones taking precedence.
    
    Args:
        *dicts: Dictionaries to merge
    
    Returns:
        Merged dictionary
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    Safely get value from dictionary with default.
    
    Args:
        dictionary: Dictionary to get value from
        key: Key to look for
        default: Default value if key not found
    
    Returns:
        Value from dictionary or default
    """
    try:
        return dictionary.get(key, default)
    except (AttributeError, TypeError):
        return default


def log_performance_metrics(operation_name: str, start_time: datetime, 
                          end_time: Optional[datetime] = None, 
                          additional_metrics: Optional[Dict[str, Any]] = None):
    """
    Log performance metrics for an operation.
    
    Args:
        operation_name: Name of the operation
        start_time: Operation start time
        end_time: Operation end time (defaults to current time)
        additional_metrics: Additional metrics to log
    """
    if end_time is None:
        end_time = get_current_timestamp()
    
    duration = (end_time - start_time).total_seconds()
    
    metrics = {
        "operation": operation_name,
        "duration_seconds": duration,
        "duration_formatted": format_duration(duration),
        "start_time": format_timestamp(start_time),
        "end_time": format_timestamp(end_time)
    }
    
    if additional_metrics:
        metrics.update(additional_metrics)
    
    logger.info(f"📊 PERFORMANCE | {operation_name.upper()} | DURATION: {format_duration(duration)} | METRICS: {metrics}")
