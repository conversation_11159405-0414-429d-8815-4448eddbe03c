"""
Collection Processor Helper for Task Utils V2
Handles the main task collection processing with priority-based parallel processing.
"""

import asyncio
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId

from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

# Import helper functions
from .core_utils import map_task_type, serialize_usage_metadata
from .task_completion_handler import complete_task_fully, process_remaining_tasks_sequentially
from .logging_helper import create_task_logger, get_current_timestamp
from .utility_helper import log_performance_metrics
from .media_cache_helper import get_cache_stats

logger = setup_new_logging(__name__)


async def save_task_collection_and_items_with_priority(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server: Optional[Any] = None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    Save tasks and stories with priority-based parallel processing.

    Priority Processing:
    1. INSTANT: Text-based tasks (single_choice, multiple_choice) - saved immediately
    2. PARALLEL: Media tasks (image_identification, speak_word) - generated in background
    3. PARALLEL: Story images - generated in background with lowest priority

    Args:
        current_user: Current user context with database access
        session_id: Session identifier
        tasks_data: Output from prompt_maker_v2.py with tasks and stories
        collection_id: Optional existing collection ID, creates new one if None
        audio_storage_info: Optional MinIO storage information
        socketio_server: Optional SocketIO server for real-time updates
        use_background_tasks: Whether to use background processing

    Returns:
        Dictionary with task_set_id (collection ID) and instant text tasks ready
    """
    # Initialize structured logging
    start_time = get_current_timestamp()
    task_logger = create_task_logger(session_id, current_user.user.id)

    try:
        if not tasks_data or not tasks_data.get("tasks"):
            logger.error("❌ No tasks data provided")
            return {
                "status": "error",
                "error": "No tasks data provided",
                "task_set_id": None
            }

        # Generate collection ID if not provided (this will be the task_set_id)
        if not collection_id:
            collection_id = str(uuid.uuid4())

        tasks = tasks_data["tasks"]
        stories = tasks_data.get("stories", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        usage_metadata = serialize_usage_metadata(tasks_data.get("usage_metadata", {}))
        title = tasks_data.get("title", "Generated Task Set V2")

        # Separate tasks by priority for parallel processing
        text_tasks = []  # Highest priority - instant
        media_tasks = []  # Medium priority - parallel background

        for task in tasks:
            task_type = task.get("type", "single_choice")
            if task_type in ["single_choice", "multiple_choice", "true_false", "fill_in_blank"]:
                text_tasks.append(task)
            else:
                media_tasks.append(task)

        logger.info("=" * 100)
        logger.info("🚀 TASK COLLECTION PROCESSING STARTED")
        logger.info("=" * 100)
        logger.info(f"📊 COLLECTION: {collection_id}")
        logger.info(f"📋 TEXT TASKS (INSTANT): {len(text_tasks)}")
        logger.info(f"🎨 MEDIA TASKS (BACKGROUND): {len(media_tasks)}")
        logger.info(f"📚 STORIES (BACKGROUND): {len(stories)}")
        logger.info("-" * 100)

        # Create task set document using existing task_sets collection structure
        task_set_id = ObjectId()
        task_set_doc = {
            "_id": task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "session_id": session_id,
            "title": title,
            "input_type": InputType.AUDIO,
            "tasks": [],  # Will be populated with text tasks first, then media tasks
            "stories": [],  # Will be populated with stories (images generated in background)
            "total_tasks": len(tasks),
            "total_stories": len(stories),
            "text_tasks_ready": len(text_tasks),
            "media_tasks_pending": len(media_tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": TaskStatus.PENDING,
            "gentype": GenerationType.PRIMARY.value,
            "has_follow_up": False,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "created_at": datetime.now(timezone.utc),
            # V2 specific metadata
            "v2_collection_id": collection_id,
            "optimization_metadata": optimization_stats,
            "usage_metadata": usage_metadata,
            "service_version": "v2",
            "priority_processing": {
                "text_tasks_count": len(text_tasks),
                "media_tasks_count": len(media_tasks),
                "stories_count": len(stories),
                "processing_status": "text_ready_media_pending"
            }
        }

        # Add audio storage information if provided (same as v1)
        if audio_storage_info:
            # Store complete MinIO object information in input_content (like v1)
            input_content = {
                "object_name": audio_storage_info.get("object_name"),
                "bucket_name": audio_storage_info.get("bucket_name"),
                "object_path": audio_storage_info.get("object_path"),
                "file_name": audio_storage_info.get("file_name"),
                "content_type": audio_storage_info.get("content_type", "audio/wav"),
                "size_bytes": audio_storage_info.get("size_bytes"),
                "folder": audio_storage_info.get("folder", "recordings_v2"),
                "session_id": session_id,
                "created_at": audio_storage_info.get("created_at"),
                "file_extension": audio_storage_info.get("file_extension", ".wav"),
            }
            task_set_doc["input_content"] = input_content
            logger.debug(f"Adding input_content to task set (same as v1)")
        else:
            logger.debug("No audio_storage_info provided, input_content will not be added")

        # PRIORITY 1: Process text-based tasks INSTANTLY (no media needed)
        total_score = 0
        instant_tasks = []
        pending_media_tasks = []

        # Process text tasks first - these go to task_items collection immediately
        for task_data in text_tasks:
            try:
                task_item_id = ObjectId()
                task_type = map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {}),
                        "options_metadata": {}
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "instant",
                        "_media_ready": True
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                instant_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error processing instant task: {e}")
                continue

        # Save instant text tasks to task_items collection immediately
        if instant_tasks:
            await current_user.async_db.task_items.insert_many(instant_tasks)
            logger.info(f"✅ Saved {len(instant_tasks)} instant text tasks to task_items collection")

        return await _process_media_tasks_and_stories(
            current_user, task_set_id, task_set_doc, media_tasks, stories,
            instant_tasks, pending_media_tasks, total_score, collection_id,
            optimization_stats, socketio_server, use_background_tasks,
            start_time
        )

    except Exception as e:
        logger.error(f"❌ Error saving task collection: {e}")
        return {
            "status": "error",
            "error": str(e),
            "task_set_id": None
        }


async def _process_media_tasks_and_stories(
    current_user, task_set_id, task_set_doc, media_tasks, stories,
    instant_tasks, pending_media_tasks, total_score, collection_id,
    optimization_stats, socketio_server, use_background_tasks, start_time
):
    """Helper function to process media tasks and stories (continuation of main function)."""
    # PRIORITY 2: Save media tasks to database immediately (media will be generated in background)
    for task_data in media_tasks:
        try:
            task_item_id = ObjectId()
            task_type = map_task_type(task_data.get("type", "single_choice"))
            question_data = task_data.get("question", {})

            task_item = {
                "_id": task_item_id,
                "task_set_id": task_set_id,
                "user_id": ObjectId(current_user.user.id),
                "session_id": current_user.user.id,  # Use session from task_set_doc
                "type": task_type,
                "title": task_data.get("title", "Generated Task"),
                "question": {
                    "text": question_data.get("text", ""),
                    "translated_text": question_data.get("translated_text", ""),
                    "options": question_data.get("options", {}),
                    "answer_hint": question_data.get("answer_hint", ""),
                    "metadata": question_data.get("metadata", {})
                },
                "correct_answer": {
                    "value": question_data.get("answer", ""),
                    "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                           "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                },
                "user_answer": None,
                "status": TaskStatus.PENDING.value,
                "result": None,
                "remark": None,
                "total_score": task_data.get("total_score", 10),
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": task_data.get("difficulty_level", 2),
                "metadata": {
                    "_v2_optimized": task_data.get("_v2_optimized", False),
                    "_media_excluded": task_data.get("_media_excluded", False),
                    "_priority": "media_pending",
                    "_media_ready": False
                },
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            pending_media_tasks.append(task_item)
            total_score += task_item["total_score"]

        except Exception as e:
            logger.error(f"Error preparing media task: {e}")
            continue

    # Save media tasks to task_items collection immediately (media will be generated in background)
    if pending_media_tasks:
        await current_user.async_db.task_items.insert_many(pending_media_tasks)
        logger.info(f"✅ Saved {len(pending_media_tasks)} media tasks to task_items collection (media pending)")

    # PRIORITY 3: Process stories for background image generation
    instant_stories = []
    for story_data in stories:
        try:
            story_item_id = ObjectId()
            story_item = {
                "_id": story_item_id,
                "task_set_id": task_set_id,
                "user_id": ObjectId(current_user.user.id),
                "session_id": task_set_doc["session_id"],
                "stage": story_data.get("stage", 1),
                "script": story_data.get("script", ""),
                "image": story_data.get("image", ""),
                "metadata": {
                    **story_data.get("metadata", {}),
                    "_priority": "background_image",
                    "_image_ready": False
                },
                "task_title": story_data.get("task_title", ""),
                "task_type": story_data.get("task_type", ""),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            instant_stories.append(story_item)

        except Exception as e:
            logger.error(f"Error processing story: {e}")
            continue

    # Save instant stories to story_steps collection (images will be generated in background)
    if instant_stories:
        await current_user.async_db.story_steps.insert_many(instant_stories)
        logger.info(f"✅ Saved {len(instant_stories)} stories to story_steps collection (images pending)")

    # Continue with the rest of the processing...
    return await _finalize_collection_processing(
        current_user, task_set_id, task_set_doc, instant_stories, instant_tasks,
        pending_media_tasks, total_score, collection_id, optimization_stats,
        socketio_server, use_background_tasks, start_time
    )


async def _finalize_collection_processing(
    current_user, task_set_id, task_set_doc, instant_stories, instant_tasks,
    pending_media_tasks, total_score, collection_id, optimization_stats,
    socketio_server, use_background_tasks, start_time
):
    """Finalize collection processing with task completion and background processing."""
    # Update task set with references and metadata - SAME FORMAT AS V1
    all_processed_tasks = instant_tasks + pending_media_tasks
    task_set_doc["tasks"] = [str(task["_id"]) for task in all_processed_tasks]  # V1 format: simple array of task IDs
    task_set_doc["stories"] = [str(story["_id"]) for story in instant_stories]  # V1 format: simple array of story IDs
    task_set_doc["total_score"] = total_score

    # Save task set to existing task_sets collection
    await current_user.async_db.task_sets.insert_one(task_set_doc)

    # PRIORITY PROCESSING: Complete FIRST task fully before returning response
    first_task_completed = False
    if all_processed_tasks:
        first_task = all_processed_tasks[0]
        logger.info(f"🎯 PRIORITY: Completing first task {first_task['_id']} fully before returning response")

        try:
            # Complete the first task (options + media)
            await complete_task_fully(current_user, first_task, socketio_server)
            first_task_completed = True
            logger.info(f"✅ First task {first_task['_id']} completed successfully")
        except Exception as e:
            logger.error(f"❌ Failed to complete first task: {e}")
            first_task_completed = False

    # Start background processing for remaining tasks and stories SEQUENTIALLY
    remaining_tasks = all_processed_tasks[1:] if len(all_processed_tasks) > 1 else []
    if use_background_tasks and (remaining_tasks or instant_stories):
        try:
            logger.info(f"🔄 Starting SEQUENTIAL background processing...")

            # Create background task with proper error handling
            background_task = asyncio.create_task(process_remaining_tasks_sequentially(
                current_user, task_set_id, remaining_tasks, instant_stories, socketio_server
            ))
            # Add callback to log any exceptions
            background_task.add_done_callback(
                lambda task: logger.error(f"❌ Background task failed: {task.exception()}")
                if task.exception() else logger.debug(f"✅ Background task completed successfully")
            )
            logger.info(f"🔄 Started sequential background processing for {len(remaining_tasks)} remaining tasks and {len(instant_stories)} stories")
        except Exception as e:
            logger.error(f"❌ Failed to start background processing: {e}")
            # Continue without background processing
            pass

    remaining_tasks = all_processed_tasks[1:] if len(all_processed_tasks) > 1 else []

    # Log structured summary table
    end_time = get_current_timestamp()
    duration = (end_time - start_time).total_seconds()

    logger.info("=" * 100)
    logger.info("📊 TASK COLLECTION PROCESSING SUMMARY")
    logger.info("=" * 100)
    logger.info(f"⏱️  DURATION: {duration:.2f}s")
    logger.info(f"📋 TEXT TASKS (INSTANT): {len(instant_tasks)} ✅")
    logger.info(f"🎨 MEDIA TASKS (BACKGROUND): {len(remaining_tasks)} 🔄")
    logger.info(f"📚 STORIES (BACKGROUND): {len(instant_stories)} 🔄")
    logger.info(f"🎯 FIRST TASK: {'✅ COMPLETED' if first_task_completed else '❌ FAILED'}")
    logger.info(f"📊 TOTAL SCORE: {total_score}")
    logger.info(f"🆔 COLLECTION ID: {collection_id}")
    logger.info(f"🆔 TASK SET ID: {task_set_id}")
    logger.info("=" * 100)

    # Get and log cache statistics
    try:
        cache_stats = await get_cache_stats(current_user)
        logger.info(f"📊 CACHE STATS | TOTAL: {cache_stats.get('total_cached', 0)} | AUDIO: {cache_stats.get('audio_count', 0)} | IMAGE: {cache_stats.get('image_count', 0)}")
    except Exception as cache_error:
        logger.warning(f"⚠️ Could not retrieve cache stats: {cache_error}")

    # Log performance metrics
    log_performance_metrics(
        "task_collection_processing",
        start_time,
        end_time,
        {
            "total_tasks": len(all_processed_tasks),
            "instant_tasks": len(instant_tasks),
            "media_tasks": len(remaining_tasks),
            "stories": len(instant_stories),
            "first_task_completed": first_task_completed,
            "total_score": total_score
        }
    )

    return {
        "status": "success",
        "task_set_id": str(task_set_id),  # Return MongoDB ObjectId as string (like v1)
        "collection_metadata": {
            "total_task_sets": 1,
            "total_tasks": len(all_processed_tasks),
            "total_stories": len(instant_stories),
            "instant_tasks_ready": len(instant_tasks),
            "media_tasks_pending": len(remaining_tasks),  # Remaining tasks in background
            "stories_pending_images": len(instant_stories),
            "optimization_stats": optimization_stats,
            "v2_collection_id": collection_id,  # Keep UUID for v2 tracking
            "service_version": "v2",
            "priority_processing": True,
            "first_task_completed": first_task_completed
        },
        "instant_tasks": instant_tasks,  # Ready immediately
        "first_task_ready": first_task_completed,  # First task fully completed
        "pending_media_tasks": len(remaining_tasks),  # Remaining tasks in background
        "pending_stories": len(instant_stories)  # Images being generated in background
    }
