"""
Task Media Generator for Task Utils V2
Handles image and audio generation for individual tasks.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from bson.objectid import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.database import retry_db_operation
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio
from app.v2.api.socket_service_v2.generator.imagen import generate_image

from .media_cache_helper import check_media_cache, save_media_cache
from .core_utils import serialize_usage_metadata

logger = setup_new_logging(__name__)


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def generate_and_store_task_image(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """Generate and immediately store task image."""
    try:
        logger.info(f"🖼️ TASK {task_id} | IMAGE: PROCESSING | KEYWORD: {keyword}")

        # Check cache first
        cached_image = await check_media_cache(current_user, keyword, "image", "imagen_prompt")

        if cached_image:
            # Use cached image
            file_info = cached_image["file_info"]
            usage_metadata = cached_image["usage_metadata"]

            # Store cached image metadata
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata._priority": "image_ready",
                        "metadata._cached": True,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )
            logger.info(f"🎯 TASK {task_id} | IMAGE: CACHED | URL: {file_info.get('url')}")
            return

        # Generate new image using v1 function
        logger.info(f"🎨 TASK {task_id} | IMAGE: GENERATING")
        _file_text, file_info, usage_metadata = await generate_image(current_user, keyword)

        if file_info:
            # Store image metadata immediately
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata._priority": "image_ready",
                        "metadata._cached": False,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )

            # Save to cache for future use
            await save_media_cache(
                current_user, keyword, "image", "imagen_prompt",
                _file_text or "", file_info, usage_metadata
            )

            logger.info(f"✅ TASK {task_id} | IMAGE: GENERATED | URL: {file_info.get('url')}")
        else:
            logger.error(f"❌ TASK {task_id} | IMAGE: FAILED | No image data received")
            await mark_task_media_failed(current_user, task_id, "No image data received")

    except Exception as e:
        logger.error(f"❌ Failed to generate image for task {task_id}: {e}")
        await mark_task_media_failed(current_user, task_id, str(e))


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def generate_and_store_task_audio(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """Generate and immediately store task audio."""
    try:
        logger.info(f"🔊 TASK {task_id} | AUDIO: PROCESSING | KEYWORD: {keyword}")

        # Check cache first
        cached_audio = await check_media_cache(current_user, keyword, "audio", "audio_prompt")

        if cached_audio:
            # Use cached audio
            file_info = cached_audio["file_info"]
            usage_metadata = cached_audio["usage_metadata"]

            # Store cached audio metadata
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata._priority": "audio_ready",
                        "metadata._cached": True,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )
            logger.info(f"🎯 TASK {task_id} | AUDIO: CACHED | URL: {file_info.get('url')}")
            return

        # Generate new audio using v1 function
        logger.info(f"🎵 TASK {task_id} | AUDIO: GENERATING")
        _file_text, file_info, usage_metadata = await generate_audio(current_user, keyword, "audio_prompt")

        if file_info:
            # Store audio metadata immediately
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata._priority": "audio_ready",
                        "metadata._cached": False,
                        "updated_at": datetime.now(timezone.utc),
                        "usage": serialize_usage_metadata(usage_metadata)
                    }
                }
            )

            # Save to cache for future use
            await save_media_cache(
                current_user, keyword, "audio", "audio_prompt",
                _file_text or "", file_info, usage_metadata
            )

            logger.info(f"✅ TASK {task_id} | AUDIO: GENERATED | URL: {file_info.get('url')}")
        else:
            logger.error(f"❌ TASK {task_id} | AUDIO: FAILED | No audio data received")
            await mark_task_media_failed(current_user, task_id, "No audio data received")

    except Exception as e:
        logger.error(f"❌ Failed to generate audio for task {task_id}: {e}")
        await mark_task_media_failed(current_user, task_id, str(e))


async def generate_task_media_independent(
    current_user: UserTenantDB,
    task_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate media for a single task independently and store immediately."""
    try:
        task_id = task_item["_id"]
        task_type = str(task_item["type"])
        question = task_item.get("question", {})

        logger.info(f"🎨 Generating media for task {task_id} ({task_type})")

        # Generate task-specific media (images/audio)
        if task_type in ["image_identification", "visual_question"]:
            # Generate image for visual tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                await generate_and_store_task_image(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for image task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for image task")

        elif task_type in ["speak_word", "pronunciation", "audio_identification"]:
            # Generate audio for audio tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                await generate_and_store_task_audio(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for audio task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for audio task")

        else:
            # For choice tasks, no additional media is needed - mark as ready
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata._media_ready": True,
                        "metadata._priority": "no_additional_media_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Task {task_id} marked as ready (no additional media needed for {task_type})")

    except Exception as e:
        logger.error(f"❌ Failed to generate media for task {task_item.get('_id')}: {e}")
        await mark_task_media_failed(current_user, task_item.get('_id'), str(e))


@retry_db_operation(max_retries=3, delay=1.0, backoff=2.0)
async def mark_task_media_failed(current_user: UserTenantDB, task_id: ObjectId, error_msg: str):
    """Mark task media generation as failed."""
    try:
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._media_ready": False,
                    "metadata._priority": "media_failed",
                    "metadata.error": error_msg,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        logger.error(f"❌ Marked task {task_id} media as failed: {error_msg}")
    except Exception as db_error:
        logger.error(f"❌ Failed to mark task {task_id} as failed: {db_error}")
