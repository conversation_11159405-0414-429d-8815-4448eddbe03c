"""
Task Categorizer Helper for Task Utils V2
Categorizes tasks by type for priority processing.
"""

from typing import Dict, Any, List, Optional
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


def categorize_tasks_by_type(tasks: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Categorize tasks by type for priority processing.
    
    Categories:
    1. single_choice - Single choice questions with options audio
    2. multiple_choice - Multiple choice questions with options audio  
    3. image_audio - Tasks requiring image/audio media generation
    4. story - Story-based tasks
    
    Args:
        tasks: List of task dictionaries
        
    Returns:
        Dictionary with categorized tasks by type
    """
    categories = {
        "single_choice": [],
        "multiple_choice": [],
        "image_audio": [],
        "story": []
    }
    
    for task in tasks:
        task_type = task.get("type", "single_choice")
        
        if task_type == "single_choice":
            categories["single_choice"].append(task)
        elif task_type == "multiple_choice":
            categories["multiple_choice"].append(task)
        elif task_type in ["image", "audio", "image_audio"]:
            categories["image_audio"].append(task)
        elif task_type == "story":
            categories["story"].append(task)
        else:
            # Default to single choice for unknown types
            logger.warning(f"Unknown task type '{task_type}', defaulting to single_choice")
            categories["single_choice"].append(task)
    
    # Log categorization results
    logger.info("📊 TASK CATEGORIZATION RESULTS:")
    for category, task_list in categories.items():
        if task_list:
            logger.info(f"  {category.upper()}: {len(task_list)} tasks")
    
    return categories


def get_remaining_tasks(
    task_categories: Dict[str, List[Dict[str, Any]]],
    first_task: Optional[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    Get remaining tasks for background processing after first task completion.
    
    Args:
        task_categories: Categorized tasks by type
        first_task: The first task that was processed
        
    Returns:
        List of remaining tasks for background processing
    """
    remaining_tasks = []
    
    if not first_task:
        # If no first task was processed, all tasks are remaining
        for task_list in task_categories.values():
            remaining_tasks.extend(task_list)
        return remaining_tasks
    
    first_task_type = first_task.get("type", "single_choice")
    first_task_id = first_task.get("id") or first_task.get("_id")
    
    # Add remaining single choice tasks (skip first one if it was single choice)
    if first_task_type == "single_choice":
        # Skip the first single choice task
        remaining_single_choice = [
            task for task in task_categories["single_choice"]
            if (task.get("id") or task.get("_id")) != first_task_id
        ]
        remaining_tasks.extend(remaining_single_choice)
    else:
        # Add all single choice tasks
        remaining_tasks.extend(task_categories["single_choice"])
    
    # Add remaining multiple choice tasks (skip first one if it was multiple choice)
    if first_task_type == "multiple_choice":
        # Skip the first multiple choice task
        remaining_multiple_choice = [
            task for task in task_categories["multiple_choice"]
            if (task.get("id") or task.get("_id")) != first_task_id
        ]
        remaining_tasks.extend(remaining_multiple_choice)
    else:
        # Add all multiple choice tasks
        remaining_tasks.extend(task_categories["multiple_choice"])
    
    # Add remaining image/audio tasks (skip first one if it was image/audio)
    if first_task_type in ["image", "audio", "image_audio"]:
        # Skip the first image/audio task
        remaining_image_audio = [
            task for task in task_categories["image_audio"]
            if (task.get("id") or task.get("_id")) != first_task_id
        ]
        remaining_tasks.extend(remaining_image_audio)
    else:
        # Add all image/audio tasks
        remaining_tasks.extend(task_categories["image_audio"])
    
    # Add all story tasks (stories are always processed in background)
    remaining_tasks.extend(task_categories["story"])
    
    logger.info(f"📋 REMAINING TASKS: {len(remaining_tasks)} tasks for background processing")
    
    return remaining_tasks


def get_first_priority_task(task_categories: Dict[str, List[Dict[str, Any]]]) -> Optional[Dict[str, Any]]:
    """
    Get the first priority task based on type priority.
    
    Priority order:
    1. Single choice tasks
    2. Multiple choice tasks  
    3. Image/audio tasks
    4. Story tasks
    
    Args:
        task_categories: Categorized tasks by type
        
    Returns:
        First priority task or None if no tasks
    """
    # Priority 1: Single choice tasks
    if task_categories["single_choice"]:
        first_task = task_categories["single_choice"][0]
        logger.info(f"🎯 PRIORITY 1: Selected single choice task {first_task.get('id')} as first task")
        return first_task
    
    # Priority 2: Multiple choice tasks
    if task_categories["multiple_choice"]:
        first_task = task_categories["multiple_choice"][0]
        logger.info(f"🎯 PRIORITY 2: Selected multiple choice task {first_task.get('id')} as first task")
        return first_task
    
    # Priority 3: Image/audio tasks
    if task_categories["image_audio"]:
        first_task = task_categories["image_audio"][0]
        logger.info(f"🎯 PRIORITY 3: Selected image/audio task {first_task.get('id')} as first task")
        return first_task
    
    # Priority 4: Story tasks
    if task_categories["story"]:
        first_task = task_categories["story"][0]
        logger.info(f"🎯 PRIORITY 4: Selected story task {first_task.get('id')} as first task")
        return first_task
    
    logger.warning("⚠️ No tasks found in any category")
    return None
