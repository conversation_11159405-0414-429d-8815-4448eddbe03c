"""
Media Generation Configuration Loader
Loads and manages media generation configuration for different task types from database.
"""

from typing import Dict, Any
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName

logger = setup_new_logging(__name__)


class MediaGenerationConfig:
    """Configuration manager for media generation settings loaded from database."""

    def __init__(self, current_user: UserTenantDB, environment: str = "production"):
        """
        Initialize media generation configuration from database.

        Args:
            current_user: Current user with database access (required)
            environment: Environment (development/production)
        """
        if not current_user:
            raise ValueError("current_user is required for database-based configuration")

        self.environment = environment
        self.current_user = current_user
        self.config = self._load_config_from_database()
        self._apply_environment_overrides()
        
    def _load_config_from_database(self) -> Dict[str, Any]:
        """Load simple 5-setting configuration from database."""
        try:
            config_collection = self.current_user.db[CollectionName.CONFIG]
            config_doc = config_collection.find_one({"name": "media_generation"})

            if config_doc:
                # Remove MongoDB _id for cleaner config
                if "_id" in config_doc:
                    del config_doc["_id"]
                logger.info("✅ Loaded media generation config from database")
                return config_doc
            else:
                logger.warning("❌ Media generation config not found in database, using defaults")
                return self._get_default_config()

        except Exception as e:
            logger.error(f"❌ Error loading config from database: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration with 5 simple global settings plus followup generation control."""
        return {
            "name": "media_generation",
            "options_audio_enabled": False,
            "quiz_image_enabled": True,
            "quiz_audio_enabled": True,
            "story_image_enabled": True,
            "story_audio_enabled": True,
            "followup_generator_count": 3
        }
    

    def _apply_environment_overrides(self):
        """Apply environment-specific configuration overrides."""
        if "environment_overrides" not in self.config:
            return
            
        env_overrides = self.config["environment_overrides"].get(self.environment, {})
        
        for key_path, value in env_overrides.items():
            self._set_nested_value(self.config, key_path, value)
            logger.debug(f"Applied {self.environment} override: {key_path} = {value}")
    
    def _set_nested_value(self, config: Dict[str, Any], key_path: str, value: Any):
        """Set a nested configuration value using dot notation."""
        keys = key_path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def is_media_generation_enabled(self) -> bool:
        """Check if media generation is globally enabled."""
        return self.config.get("options", {}).get("global_settings", {}).get("enable_media_generation", True)
    
    def is_task_type_enabled(self, task_type: str) -> bool:
        """Check if a specific task type is enabled for media generation."""
        task_config = self.config.get("options", {}).get("task_types", {}).get(task_type, {})
        return task_config.get("enabled", False)
    
    def is_options_audio_enabled(self, task_type: str = None) -> bool:
        """Check if options audio generation is enabled globally."""
        return self.config.get("options_audio_enabled", True)
    
    def is_question_image_enabled(self, task_type: str = None) -> bool:
        """Check if question image generation is enabled globally."""
        return self.config.get("quiz_image_enabled", True)
    
    def is_question_audio_enabled(self, task_type: str = None) -> bool:
        """Check if question audio generation is enabled globally."""
        return self.config.get("quiz_audio_enabled", True)

    def is_quiz_image_enabled(self, task_type: str = None) -> bool:
        """Check if quiz image generation is enabled globally."""
        return self.config.get("quiz_image_enabled", True)

    def is_quiz_audio_enabled(self, task_type: str = None) -> bool:
        """Check if quiz audio generation is enabled globally."""
        return self.config.get("quiz_audio_enabled", True)

    def is_story_image_enabled(self, task_type: str = None) -> bool:
        """Check if story image generation is enabled globally."""
        return self.config.get("story_image_enabled", True)

    def is_story_audio_enabled(self, task_type: str = None) -> bool:
        """Check if story audio generation is enabled globally."""
        return self.config.get("story_audio_enabled", True)

    def get_task_priority(self, task_type: str) -> int:
        """Get the processing priority for a task type."""
        task_config = self.config.get("options", {}).get("task_types", {}).get(task_type, {})
        return task_config.get("priority", 999)
    
    def get_processing_order(self) -> list:
        """Get the task processing order."""
        return self.config.get("options", {}).get("processing_order", {}).get("sequence", [])
    
    def get_rate_limiting_delay(self, delay_type: str = "delay_between_options") -> int:
        """Get rate limiting delay in milliseconds."""
        rate_limiting = self.config.get("options", {}).get("global_settings", {}).get("rate_limiting", {})
        return rate_limiting.get(delay_type, 2000)
    
    def get_voice_settings(self, task_type: str, media_type: str = "options_audio") -> Dict[str, Any]:
        """Get voice settings for audio generation."""
        task_config = self.config.get("options", {}).get("task_types", {}).get(task_type, {})
        media_config = task_config.get("media_generation", {})
        audio_config = media_config.get(media_type, {})
        return audio_config.get("voice_settings", {})
    
    def get_image_settings(self, task_type: str, media_type: str = "question_image") -> Dict[str, Any]:
        """Get image generation settings."""
        task_config = self.config.get("options", {}).get("task_types", {}).get(task_type, {})
        media_config = task_config.get("media_generation", {})
        image_config = media_config.get(media_type, {})
        return {
            "dimensions": image_config.get("dimensions", {"width": 512, "height": 512}),
            "quality": image_config.get("quality", "high"),
            "format": image_config.get("format", "png"),
            "style": image_config.get("style", "educational")
        }
    
    def is_caching_enabled(self, task_type: str = None, media_type: str = None) -> bool:
        """Check if caching is enabled globally or for specific media type."""
        # Check global caching
        global_cache = self.config.get("options", {}).get("global_settings", {}).get("enable_caching", True)
        
        if not task_type or not media_type:
            return global_cache
        
        # Check specific media type caching
        task_config = self.config.get("options", {}).get("task_types", {}).get(task_type, {})
        media_config = task_config.get("media_generation", {})
        specific_config = media_config.get(media_type, {})
        
        return specific_config.get("cache_enabled", global_cache)
    
    def should_return_response_after_first_task(self) -> bool:
        """Check if response should be returned after first task completion."""
        processing_order = self.config.get("options", {}).get("processing_order", {})
        first_task_config = processing_order.get("first_task_completion", {})
        return first_task_config.get("background_processing", True)

    def get_followup_generator_count(self) -> int:
        """Get the maximum number of followup levels to generate."""
        return self.config.get("followup_generator_count", 3)
    
    def get_first_task_type(self) -> str:
        """Get the first task type to process."""
        processing_order = self.config.get("options", {}).get("processing_order", {})
        sequence = processing_order.get("sequence", ["single_choice"])
        return sequence[0] if sequence else "single_choice"

    def save_config_to_database(self) -> bool:
        """
        Save current configuration to database.

        Returns:
            bool: True if saved successfully, False otherwise
        """
        if not self.current_user:
            logger.error("❌ Cannot save to database: no current_user provided")
            return False

        try:
            config_collection = self.current_user.db[CollectionName.CONFIG]

            # Prepare config document
            config_doc = self.config.copy()
            config_doc["name"] = "media_generation"
            config_doc["last_updated"] = "2025-07-04T06:59:51Z"  # You might want to use datetime.utcnow()

            # Upsert the configuration
            result = config_collection.replace_one(
                {"name": "media_generation"},
                config_doc,
                upsert=True
            )

            if result.upserted_id or result.modified_count > 0:
                logger.info("✅ Media generation config saved to database successfully")
                return True
            else:
                logger.warning("⚠️ No changes made to database config")
                return True

        except Exception as e:
            logger.error(f"❌ Error saving config to database: {e}")
            return False


def get_media_config(current_user: UserTenantDB, environment: str = "production") -> MediaGenerationConfig:
    """
    Get the media configuration instance from database.

    Args:
        current_user: Current user with database access (required)
        environment: Environment (development/production)

    Returns:
        MediaGenerationConfig instance loaded from database
    """
    return MediaGenerationConfig(current_user=current_user, environment=environment)

def reload_media_config(current_user: UserTenantDB, environment: str = "production") -> MediaGenerationConfig:
    """
    Reload the media configuration from database.

    Args:
        current_user: Current user with database access (required)
        environment: Environment (development/production)

    Returns:
        MediaGenerationConfig instance loaded fresh from database
    """
    return MediaGenerationConfig(current_user=current_user, environment=environment)
