"""
Task Processing Helper for Task Utils V2
Handles core task processing logic, media generation, and sequential processing.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
from bson.objectid import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio
from app.v2.api.socket_service_v2.generator.imagen import generate_image
from .media_cache_helper import check_media_cache, save_media_cache
from .websocket_helper import send_websocket_notification

logger = setup_new_logging(__name__)


async def complete_task_fully(current_user: UserTenantDB, task_id: str, task_data: Dict[str, Any], 
                             task_number: int, total_tasks: int, session_id: str) -> bool:
    """
    Complete a task fully with all media generation and options audio.
    
    Args:
        current_user: User context with database access
        task_id: Task ID to complete
        task_data: Task data from database
        task_number: Current task number
        total_tasks: Total number of tasks
        session_id: WebSocket session ID
    
    Returns:
        True if task completed successfully, False otherwise
    """
    try:
        logger.info(f"📋 TASK {task_number}/{total_tasks} | ID: {task_id} | TYPE: {task_data.get('task_type')} | STATUS: PROCESSING")
        
        # Generate task media (image/audio) if needed
        media_success = await generate_task_media(current_user, task_id, task_data)
        
        # Generate options audio
        options_success = await generate_options_audio_for_task(current_user, task_id, task_data)
        
        # Update task completion status
        success = media_success and options_success
        
        if success:
            await current_user.async_db.task_items.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {"completed_at": datetime.now(timezone.utc), "status": "completed"}}
            )
            logger.info(f"✅ TASK {task_number}/{total_tasks} | ID: {task_id} | STATUS: COMPLETED")
        else:
            logger.error(f"❌ TASK {task_number}/{total_tasks} | ID: {task_id} | STATUS: FAILED")
        
        # Send WebSocket notification
        await send_websocket_notification(
            current_user, session_id, "task_completed",
            {"task_id": task_id, "success": success, "task_number": task_number, "total_tasks": total_tasks}
        )
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error completing task {task_id}: {e}")
        return False


async def generate_task_media(current_user: UserTenantDB, task_id: str, task_data: Dict[str, Any]) -> bool:
    """
    Generate media (image/audio) for a task if needed.
    
    Args:
        current_user: User context with database access
        task_id: Task ID
        task_data: Task data from database
    
    Returns:
        True if media generated/cached successfully or not needed, False if failed
    """
    try:
        task_type = task_data.get("task_type")
        
        # Check if task needs media
        if task_type not in ["image_audio_task", "audio_task", "image_task"]:
            logger.info(f"🎨 TASK {task_id} | MEDIA: NOT_NEEDED")
            return True
        
        # Generate image if needed
        if "image" in task_type:
            image_success = await generate_task_image(current_user, task_id, task_data)
            if not image_success:
                return False
        
        # Generate audio if needed
        if "audio" in task_type:
            audio_success = await generate_task_audio(current_user, task_id, task_data)
            if not audio_success:
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error generating task media for {task_id}: {e}")
        return False


async def generate_task_image(current_user: UserTenantDB, task_id: str, task_data: Dict[str, Any]) -> bool:
    """Generate or retrieve cached image for task."""
    try:
        keyword = task_data.get("question", "")
        if not keyword:
            logger.warning(f"⚠️ TASK {task_id} | IMAGE: NO_KEYWORD")
            return True
        
        # Check cache first
        cached_image = await check_media_cache(current_user, keyword, "image", "imagen_prompt")
        
        if cached_image:
            # Update task with cached image (using correct field name)
            await current_user.async_db.task_items.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {
                    "question.metadata": cached_image["file_info"],
                    "metadata._media_ready": True,
                    "metadata._cached": True,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )
            logger.info(f"🎨 TASK {task_id} | IMAGE: CACHED")
            return True
        
        # Generate new image
        logger.info(f"🎨 TASK {task_id} | IMAGE: GENERATING")
        image_result = await generate_image(current_user, keyword)
        
        if image_result and "file_info" in image_result:
            # Save to database (using correct field name)
            await current_user.async_db.task_items.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {
                    "question.metadata": image_result["file_info"],
                    "metadata._media_ready": True,
                    "metadata._cached": False,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )
            
            # Save to cache
            await save_media_cache(
                current_user, keyword, "image", "imagen_prompt",
                image_result.get("file_text", ""),
                image_result["file_info"],
                image_result.get("usage_metadata", {})
            )
            
            logger.info(f"🎨 TASK {task_id} | IMAGE: GENERATED")
            return True
        else:
            logger.error(f"❌ TASK {task_id} | IMAGE: FAILED")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error generating task image for {task_id}: {e}")
        return False


async def generate_task_audio(current_user: UserTenantDB, task_id: str, task_data: Dict[str, Any]) -> bool:
    """Generate or retrieve cached audio for task."""
    try:
        keyword = task_data.get("question", "")
        if not keyword:
            logger.warning(f"⚠️ TASK {task_id} | AUDIO: NO_KEYWORD")
            return True
        
        # Check cache first
        cached_audio = await check_media_cache(current_user, keyword, "audio", "audio_prompt")
        
        if cached_audio:
            # Update task with cached audio
            await current_user.async_db.task_items.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {
                    "audio_file_text": cached_audio["file_text"],
                    "audio_file_info": cached_audio["file_info"],
                    "audio_usage_metadata": cached_audio["usage_metadata"]
                }}
            )
            logger.info(f"🎵 TASK {task_id} | AUDIO: CACHED")
            return True
        
        # Generate new audio
        logger.info(f"🎵 TASK {task_id} | AUDIO: GENERATING")
        audio_result = await generate_audio(current_user, keyword)
        
        if audio_result and "file_info" in audio_result:
            # Save to database
            await current_user.async_db.task_items.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {
                    "audio_file_text": audio_result.get("file_text", ""),
                    "audio_file_info": audio_result["file_info"],
                    "audio_usage_metadata": audio_result.get("usage_metadata", {})
                }}
            )
            
            # Save to cache
            await save_media_cache(
                current_user, keyword, "audio", "audio_prompt",
                audio_result.get("file_text", ""),
                audio_result["file_info"],
                audio_result.get("usage_metadata", {})
            )
            
            logger.info(f"🎵 TASK {task_id} | AUDIO: GENERATED")
            return True
        else:
            logger.error(f"❌ TASK {task_id} | AUDIO: FAILED")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error generating task audio for {task_id}: {e}")
        return False


async def generate_options_audio_for_task(current_user: UserTenantDB, task_id: str, task_data: Dict[str, Any]) -> bool:
    """
    Generate audio for all task options sequentially.
    
    Args:
        current_user: User context with database access
        task_id: Task ID
        task_data: Task data from database
    
    Returns:
        True if all options audio generated/cached successfully, False otherwise
    """
    try:
        options = task_data.get("options", [])
        if not options:
            logger.info(f"🔊 TASK {task_id} | OPTIONS: NONE")
            return True
        
        logger.info(f"🔊 TASK {task_id} | OPTIONS: {len(options)} total")
        
        options_metadata = {}
        success_count = 0
        cached_count = 0
        
        # Process options sequentially to avoid rate limiting
        for i, option in enumerate(options):
            option_key = option.get("key", f"option_{i}")
            option_text = option.get("text", "")
            
            if not option_text:
                logger.warning(f"⚠️ TASK {task_id} | OPTION {option_key}: NO_TEXT")
                continue
            
            # Check cache first
            cached_audio = await check_media_cache(current_user, option_text, "audio", "audio_prompt")
            
            if cached_audio:
                options_metadata[option_key] = {
                    "audio_file_text": cached_audio["file_text"],
                    "audio_file_info": cached_audio["file_info"],
                    "audio_usage_metadata": cached_audio["usage_metadata"]
                }
                cached_count += 1
                success_count += 1
                logger.info(f"🎵 TASK {task_id} | OPTION {option_key}: CACHED")
            else:
                # Generate new audio
                logger.info(f"🎵 TASK {task_id} | OPTION {option_key}: GENERATING")
                audio_result = await generate_audio(current_user, option_text)
                
                if audio_result and "file_info" in audio_result:
                    options_metadata[option_key] = {
                        "audio_file_text": audio_result.get("file_text", ""),
                        "audio_file_info": audio_result["file_info"],
                        "audio_usage_metadata": audio_result.get("usage_metadata", {})
                    }
                    
                    # Save to cache
                    await save_media_cache(
                        current_user, option_text, "audio", "audio_prompt",
                        audio_result.get("file_text", ""),
                        audio_result["file_info"],
                        audio_result.get("usage_metadata", {})
                    )
                    
                    success_count += 1
                    logger.info(f"🎵 TASK {task_id} | OPTION {option_key}: GENERATED")
                else:
                    logger.error(f"❌ TASK {task_id} | OPTION {option_key}: FAILED")
            
            # Add delay between options to avoid rate limiting
            if i < len(options) - 1:
                await asyncio.sleep(2)
        
        # Update task with options metadata
        if options_metadata:
            await current_user.async_db.task_items.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {"options_metadata": options_metadata}}
            )
        
        failed_count = len(options) - success_count
        logger.info(f"🔊 TASK {task_id} | OPTIONS SUMMARY: {success_count} success ({cached_count} cached), {failed_count} failed")
        
        return success_count > 0  # Consider success if at least one option succeeded
        
    except Exception as e:
        logger.error(f"❌ Error generating options audio for task {task_id}: {e}")
        return False


async def process_tasks_sequentially(current_user: UserTenantDB, task_ids: List[str], session_id: str) -> Dict[str, Any]:
    """
    Process multiple tasks sequentially to avoid rate limiting.
    
    Args:
        current_user: User context with database access
        task_ids: List of task IDs to process
        session_id: WebSocket session ID
    
    Returns:
        Processing summary with success/failure counts
    """
    try:
        total_tasks = len(task_ids)
        completed_tasks = 0
        failed_tasks = 0
        
        logger.info(f"🚀 SEQUENTIAL PROCESSING | TASKS: {total_tasks}")
        
        for i, task_id in enumerate(task_ids):
            task_number = i + 1
            
            # Get task data
            task_data = await current_user.async_db.task_items.find_one({"_id": ObjectId(task_id)})
            if not task_data:
                logger.error(f"❌ TASK {task_number}/{total_tasks} | ID: {task_id} | ERROR: NOT_FOUND")
                failed_tasks += 1
                continue
            
            # Process task
            success = await complete_task_fully(current_user, task_id, task_data, task_number, total_tasks, session_id)
            
            if success:
                completed_tasks += 1
            else:
                failed_tasks += 1
            
            # Add delay between tasks to avoid rate limiting
            if i < len(task_ids) - 1:
                await asyncio.sleep(3)
        
        summary = {
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": f"{(completed_tasks/total_tasks*100):.1f}%" if total_tasks > 0 else "0%"
        }
        
        logger.info(f"📊 SEQUENTIAL PROCESSING COMPLETE | {summary}")
        
        return summary
        
    except Exception as e:
        logger.error(f"❌ Error in sequential task processing: {e}")
        return {
            "total_tasks": len(task_ids),
            "completed_tasks": 0,
            "failed_tasks": len(task_ids),
            "success_rate": "0%",
            "error": str(e)
        }
