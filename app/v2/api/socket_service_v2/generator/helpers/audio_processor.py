"""
Audio Processor Helper for Task Utils V2
Handles audio processing with the V2 prompt maker.
"""

from typing import Dict, Any
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.prompt_maker_v2 import generate as generate_v2
from .core_utils import retry_with_exponential_backoff

logger = setup_new_logging(__name__)


async def process_audio_with_prompt_maker_v2(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    num_tasks: int = 4
) -> Dict[str, Any]:
    """
    Process audio with the optimized V2 prompt maker.
    
    Args:
        current_user: Current user context
        audio_bytes: Audio data to process
        num_tasks: Number of tasks to generate
        
    Returns:
        Parsed tasks data from prompt_maker_v2.py
    """
    try:
        logger.info(f"Processing {len(audio_bytes)} bytes of audio with prompt_maker V2")

        # Call the V2 prompt maker with retry logic
        result = await retry_with_exponential_backoff(
            generate_v2,
            audio_bytes,
            num_tasks,
            current_user
        )
        
        if not result or not result.get("tasks"):
            error_msg = "No task items returned from prompt_maker_v2.generate"
            logger.error(error_msg)
            return {
                "tasks": [],
                "error": error_msg,
                "status": "error",
                "optimization_stats": {},
                "usage_metadata": {}
            }

        logger.info(f"✅ Generated {len(result['tasks'])} tasks with V2 optimizations")
        return result

    except Exception as e:
        logger.error(f"❌ Error processing audio with prompt_maker V2: {e}")
        return {
            "tasks": [],
            "error": str(e),
            "status": "error",
            "optimization_stats": {},
            "usage_metadata": {}
        }
