"""
Media Configuration Usage Example
Shows how to integrate the database-loaded media generation configuration into task processing.
"""

from .media_config_loader import get_media_config
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


async def example_task_processing_with_config(current_user):
    """Example of how to use media configuration in task processing."""
    
    if not current_user:
        raise ValueError("current_user is required for database-based configuration")
    
    # Get the media configuration from database
    config = get_media_config(current_user=current_user, environment="production")
    
    # Example task data
    tasks = [
        {"id": "1", "type": "single_choice", "question": {"text": "What is this?", "options": ["आमा", "बुबा", "छोरा"]}},
        {"id": "2", "type": "image_quiz", "question": {"text": "Identify the object", "options": ["घर", "गाडी", "रुख"]}},
        {"id": "3", "type": "story", "content": "Once upon a time..."}
    ]
    
    # Check if media generation is globally enabled
    if not config.is_media_generation_enabled():
        logger.info("❌ Media generation is disabled globally")
        return
    
    # Get processing order from config
    processing_order = config.get_processing_order()
    logger.info(f"📋 Processing order: {processing_order}")
    
    # Sort tasks by priority based on config
    sorted_tasks = sorted(tasks, key=lambda t: config.get_task_priority(t["type"]))
    
    for i, task in enumerate(sorted_tasks):
        task_type = task["type"]
        task_id = task["id"]
        
        logger.info(f"🔄 Processing task {i+1}/{len(sorted_tasks)}: {task_type} (ID: {task_id})")
        
        # Check if this task type is enabled
        if not config.is_task_type_enabled(task_type):
            logger.info(f"⏭️ Task type '{task_type}' is disabled, skipping")
            continue
        
        # Process based on task type and configuration
        await process_task_with_config(task, config)
        
        # Apply rate limiting delay
        if i < len(sorted_tasks) - 1:  # Don't delay after last task
            delay = config.get_rate_limiting_delay("delay_between_tasks")
            logger.info(f"⏱️ Applying rate limiting delay: {delay}ms")
            # await asyncio.sleep(delay / 1000)  # Convert to seconds
    
    logger.info("✅ All tasks processed according to configuration")


async def process_task_with_config(task, config):
    """Process a single task using configuration settings."""
    task_type = task["type"]
    task_id = task["id"]
    
    logger.info(f"🎯 Processing {task_type} task {task_id} with config-driven approach")
    
    # Single Choice Tasks
    if task_type == "single_choice":
        await process_single_choice_with_config(task, config)
    
    # Multiple Choice Tasks  
    elif task_type == "multiple_choice":
        await process_multiple_choice_with_config(task, config)
    
    # Image Quiz Tasks
    elif task_type == "image_quiz":
        await process_image_quiz_with_config(task, config)
    
    # Audio Quiz Tasks
    elif task_type == "audio_quiz":
        await process_audio_quiz_with_config(task, config)
    
    # Story Tasks
    elif task_type == "story":
        await process_story_with_config(task, config)
    
    else:
        logger.warning(f"⚠️ Unknown task type: {task_type}")


async def process_single_choice_with_config(task, config):
    """Process single choice task with configuration."""
    task_id = task["id"]
    
    # Check if options audio is enabled
    if config.is_options_audio_enabled("single_choice"):
        logger.info(f"🎵 Generating options audio for single choice task {task_id}")
        
        # Get voice settings from config
        voice_settings = config.get_voice_settings("single_choice", "options_audio")
        logger.info(f"🎤 Voice settings: {voice_settings}")
        
        # Check if caching is enabled
        cache_enabled = config.is_caching_enabled("single_choice", "options_audio")
        logger.info(f"💾 Caching enabled: {cache_enabled}")
        
        # Get rate limiting delay for options
        delay = config.get_rate_limiting_delay("delay_between_options")
        logger.info(f"⏱️ Delay between options: {delay}ms")
        
        # Process each option
        options = task.get("question", {}).get("options", [])
        for i, option in enumerate(options):
            logger.info(f"🎵 Processing option {i+1}/{len(options)}: {option}")
            
            # Here you would call the actual audio generation function
            # await generate_option_audio(option, voice_settings, cache_enabled)
            
            # Apply delay between options (except for last option)
            if i < len(options) - 1:
                # await asyncio.sleep(delay / 1000)
                pass
    
    else:
        logger.info(f"⏭️ Options audio disabled for single choice task {task_id}")


async def process_multiple_choice_with_config(task, config):
    """Process multiple choice task with configuration."""
    # Similar to single choice processing
    await process_single_choice_with_config(task, config)


async def process_image_quiz_with_config(task, config):
    """Process image quiz task with configuration."""
    task_id = task["id"]
    
    # Check if question image generation is enabled
    if config.is_question_image_enabled("image_quiz"):
        logger.info(f"🎨 Generating question image for image quiz task {task_id}")
        
        # Get image settings from config
        image_settings = config.get_image_settings("image_quiz", "question_image")
        logger.info(f"🖼️ Image settings: {image_settings}")
        
        # Check if caching is enabled
        cache_enabled = config.is_caching_enabled("image_quiz", "question_image")
        logger.info(f"💾 Image caching enabled: {cache_enabled}")
        
        # Here you would call the actual image generation function
        # await generate_question_image(task["question"]["text"], image_settings, cache_enabled)
    
    # Check if options audio is enabled
    if config.is_options_audio_enabled("image_quiz"):
        logger.info(f"🎵 Generating options audio for image quiz task {task_id}")
        
        # Process options audio similar to single choice
        voice_settings = config.get_voice_settings("image_quiz", "options_audio")
        cache_enabled = config.is_caching_enabled("image_quiz", "options_audio")
        
        # Here you would process options audio
        # await process_options_audio(task, voice_settings, cache_enabled)


async def process_audio_quiz_with_config(task, config):
    """Process audio quiz task with configuration."""
    task_id = task["id"]
    
    # Check if question audio generation is enabled
    if config.is_question_audio_enabled("audio_quiz"):
        logger.info(f"🎵 Generating question audio for audio quiz task {task_id}")
        
        voice_settings = config.get_voice_settings("audio_quiz", "question_audio")
        cache_enabled = config.is_caching_enabled("audio_quiz", "question_audio")
        
        # Here you would generate question audio
        # await generate_question_audio(task["question"]["text"], voice_settings, cache_enabled)
    
    # Also process options audio if enabled
    if config.is_options_audio_enabled("audio_quiz"):
        logger.info(f"🎵 Generating options audio for audio quiz task {task_id}")
        # Process options audio similar to other tasks


async def process_story_with_config(task, config):
    """Process story task with configuration."""
    task_id = task["id"]
    
    # Check if story image generation is enabled
    if config.is_question_image_enabled("story"):  # Using question_image for story images
        logger.info(f"🎨 Generating story image for task {task_id}")
        
        image_settings = config.get_image_settings("story", "story_image")
        cache_enabled = config.is_caching_enabled("story", "story_image")
        
        # Here you would generate story image
        # await generate_story_image(task["content"], image_settings, cache_enabled)
    
    # Check if story audio is enabled
    if config.is_question_audio_enabled("story"):  # Using question_audio for story audio
        logger.info(f"🎵 Generating story audio for task {task_id}")
        
        voice_settings = config.get_voice_settings("story", "story_audio")
        cache_enabled = config.is_caching_enabled("story", "story_audio")
        
        # Here you would generate story audio
        # await generate_story_audio(task["content"], voice_settings, cache_enabled)


def example_config_queries(current_user):
    """Example of various configuration queries."""
    if not current_user:
        raise ValueError("current_user is required for database-based configuration")
    
    config = get_media_config(current_user=current_user)
    
    # Basic checks
    print(f"Media generation enabled: {config.is_media_generation_enabled()}")
    print(f"Single choice enabled: {config.is_task_type_enabled('single_choice')}")
    print(f"Options audio for single choice: {config.is_options_audio_enabled('single_choice')}")
    
    # Processing order
    print(f"Processing order: {config.get_processing_order()}")
    print(f"First task type: {config.get_first_task_type()}")
    
    # Rate limiting
    print(f"Delay between options: {config.get_rate_limiting_delay('delay_between_options')}ms")
    print(f"Delay between tasks: {config.get_rate_limiting_delay('delay_between_tasks')}ms")
    
    # Voice settings
    voice_settings = config.get_voice_settings('single_choice', 'options_audio')
    print(f"Voice settings for single choice: {voice_settings}")
    
    # Image settings
    image_settings = config.get_image_settings('image_quiz', 'question_image')
    print(f"Image settings for image quiz: {image_settings}")
    
    # Caching
    print(f"Global caching: {config.is_caching_enabled()}")
    print(f"Options audio caching: {config.is_caching_enabled('single_choice', 'options_audio')}")
    
    # Background processing
    print(f"Return response after first task: {config.should_return_response_after_first_task()}")


# Example integration with existing task processing
async def integrate_with_existing_task_processor(current_user, tasks_data):
    """Example of integrating config with existing task processing."""
    
    # Load configuration from database
    config = get_media_config(current_user=current_user)
    
    # Check if media generation is enabled
    if not config.is_media_generation_enabled():
        logger.info("📋 Media generation disabled, processing tasks without media")
        return await process_tasks_without_media(tasks_data)
    
    # Get processing order and priorities
    processing_order = config.get_processing_order()
    first_task_type = config.get_first_task_type()
    
    logger.info(f"🎯 Processing tasks with media generation enabled")
    logger.info(f"📋 Processing order: {processing_order}")
    logger.info(f"🥇 First task type: {first_task_type}")
    
    # Process tasks according to configuration
    return await process_tasks_with_media_config(tasks_data, config)


async def process_tasks_without_media(tasks_data):
    """Process tasks without media generation."""
    logger.info("⚡ Fast processing without media generation")
    # Your existing fast processing logic here
    return {"status": "completed", "media_generated": False}


async def process_tasks_with_media_config(tasks_data, config):
    """Process tasks with media generation according to configuration."""
    logger.info("🎨 Processing with media generation enabled")
    
    # Apply configuration-driven processing
    # This would integrate with your existing task_utils_v2 functions
    
    return {"status": "completed", "media_generated": True, "config_applied": True}
