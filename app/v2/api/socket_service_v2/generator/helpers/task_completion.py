"""
Task Completion Helper for Task Utils V2
Handles individual task completion and processing.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from bson.objectid import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

from .websocket_helper import send_websocket_notification
from .options_processor import generate_options_audio
from .media_generator import generate_and_store_task_image, generate_and_store_task_audio, mark_task_media_failed

logger = setup_new_logging(__name__)


async def complete_task_fully(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """
    Complete a single task with full processing.

    Processing order:
    1. Generate audio for ALL options (if task has options)
    2. Generate task-specific media (image/audio)

    This ensures the task is completely ready before moving to next task.
    """
    try:
        task_id = task["_id"]
        task_type = str(task["type"])
        question = task.get("question", {})

        logger.info(f"🎯 COMPLETING TASK {task_id} ({task_type})")

        # STEP 1: Generate audio for ALL options (if task has options)
        if question.get("options"):
            logger.info(f"🎵 STEP 1: Generating options audio for task {task_id}")
            await generate_options_audio(current_user, task, task_id, socketio_server)
        else:
            logger.info(f"ℹ️ Task {task_id} has no options - skipping options audio")

        # STEP 2: Generate task-specific media
        if task_type in ["image_identification", "visual_question"]:
            # Generate image for visual tasks
            keyword = question.get("answer_hint") or question.get("answer") or task.get("title", "")
            if keyword:
                logger.info(f"🖼️ STEP 2: Generating image for task {task_id} with keyword: {keyword}")
                await generate_and_store_task_image(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for image task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for image task")

        elif task_type in ["speak_word", "pronunciation", "audio_identification"]:
            # Generate audio for audio tasks
            keyword = question.get("answer_hint") or question.get("answer") or task.get("title", "")
            if keyword:
                logger.info(f"🔊 STEP 2: Generating audio for task {task_id} with keyword: {keyword}")
                await generate_and_store_task_audio(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for audio task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for audio task")

        else:
            logger.info(f"ℹ️ Task {task_id} ({task_type}) doesn't require additional media")

        # Mark task as fully completed
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._media_ready": True,
                    "metadata._priority": "fully_completed",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ TASK {task_id} FULLY COMPLETED")

        # Send completion notification
        await send_websocket_notification(socketio_server, "task_completed", {
            "task_id": str(task_id),
            "task_type": task_type,
            "status": "completed",
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Failed to complete task {task.get('_id')}: {e}")
        await mark_task_media_failed(current_user, task.get('_id'), str(e))


async def generate_task_media_independent(
    current_user: UserTenantDB,
    task_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate media for a single task independently and store immediately."""
    try:
        task_id = task_item["_id"]
        task_type = str(task_item["type"])
        question = task_item.get("question", {})

        logger.info(f"🎨 Generating media for task {task_id} ({task_type})")

        # PRIORITY 1: Generate audio for ALL options (if task has options)
        if question.get("options"):
            await generate_options_audio(current_user, task_item, task_id, socketio_server)

        # PRIORITY 2: Generate task-specific media (images/audio)
        if task_type in ["image_identification", "visual_question"]:
            # Generate image for visual tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                await generate_and_store_task_image(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for image task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for image task")

        elif task_type in ["speak_word", "pronunciation", "audio_identification"]:
            # Generate audio for audio tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                await generate_and_store_task_audio(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for audio task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for audio task")

        # Mark task as fully completed
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._media_ready": True,
                    "metadata._priority": "fully_completed",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ Task {task_id} media generation completed")

        # Send completion notification
        await send_websocket_notification(socketio_server, "task_media_completed", {
            "task_id": str(task_id),
            "task_type": task_type,
            "status": "media_ready",
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Failed to generate media for task {task_item.get('_id')}: {e}")
        await mark_task_media_failed(current_user, task_item.get('_id'), str(e))


async def process_tasks_sequentially(
    current_user: UserTenantDB,
    tasks: list[Dict[str, Any]],
    socketio_server: Optional[Any] = None,
    delay_seconds: int = 3
):
    """
    Process multiple tasks sequentially with delays to avoid rate limiting.
    
    Args:
        current_user: User context with database access
        tasks: List of tasks to process
        socketio_server: Optional SocketIO server for notifications
        delay_seconds: Delay between tasks in seconds
    """
    try:
        logger.info(f"🔄 Starting sequential processing of {len(tasks)} tasks")
        
        for i, task in enumerate(tasks):
            try:
                task_id = task.get("_id")
                logger.info(f"📋 Processing task {i+1}/{len(tasks)}: {task_id}")
                
                # Process each task sequentially - wait for completion before next
                await generate_task_media_independent(current_user, task, socketio_server)
                
                # Add delay between tasks to avoid rate limits
                if i < len(tasks) - 1:  # Don't delay after last task
                    logger.info(f"⏳ Waiting {delay_seconds} seconds before next task to avoid rate limits...")
                    await asyncio.sleep(delay_seconds)
                    
            except Exception as e:
                logger.error(f"❌ Failed to process task {task.get('_id')}: {e}")
                continue
        
        logger.info(f"🔄 Completed sequential processing of {len(tasks)} tasks")
        
    except Exception as e:
        logger.error(f"❌ Error in sequential task processing: {e}")


async def process_remaining_tasks_sequentially(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    remaining_tasks: list[Dict[str, Any]],
    pending_stories: list[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """
    Process remaining tasks and stories sequentially in background.

    Processing order:
    1. Complete remaining tasks one by one (with delays)
    2. Process stories one by one (with delays)
    """
    # Add initial delay before starting background processing
    logger.info(f"⏳ Waiting 3 seconds before starting background processing to avoid rate limits...")
    await asyncio.sleep(3)

    # Process remaining tasks sequentially
    if remaining_tasks:
        logger.info(f"🚀 Starting SEQUENTIAL processing of {len(remaining_tasks)} remaining tasks")
        await process_tasks_sequentially(current_user, remaining_tasks, socketio_server, delay_seconds=3)
        logger.info(f"🔄 Completed sequential processing of {len(remaining_tasks)} tasks")

    # Process stories sequentially
    if pending_stories:
        logger.info(f"🚀 Starting SEQUENTIAL processing of {len(pending_stories)} stories")
        
        # Import here to avoid circular imports
        from .story_processor import process_stories_sequentially
        
        await process_stories_sequentially(current_user, pending_stories, socketio_server, delay_seconds=3)
        logger.info(f"🔄 Completed sequential processing of {len(pending_stories)} stories")

    total_processed = len(remaining_tasks) + len(pending_stories)
    logger.info(f"🎉 Completed ALL sequential background processing: {len(remaining_tasks)} tasks + {len(pending_stories)} stories = {total_processed} items")


async def process_media_in_background(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    pending_media_tasks: list[Dict[str, Any]],
    pending_stories: list[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """
    Process media tasks and story images in parallel background.
    
    This function handles the background processing of media generation
    for both tasks and stories using sequential processing to avoid rate limits.
    """
    try:
        logger.info("=" * 100)
        logger.info("🚀 BACKGROUND MEDIA PROCESSING STARTED")
        logger.info("=" * 100)
        logger.info(f"🎨 MEDIA TASKS: {len(pending_media_tasks)}")
        logger.info(f"📚 STORIES: {len(pending_stories)}")
        logger.info("-" * 100)

        # PRIORITY 1: Process task media generation SEQUENTIALLY (to avoid rate limits)
        if pending_media_tasks:
            logger.info(f"🚀 Starting SEQUENTIAL processing of {len(pending_media_tasks)} task media generations")
            await process_tasks_sequentially(current_user, pending_media_tasks, socketio_server, delay_seconds=3)

        # PRIORITY 2: Process story images SEQUENTIALLY (to avoid rate limits)
        if pending_stories:
            logger.info(f"🚀 Starting SEQUENTIAL processing of {len(pending_stories)} story images")
            
            # Import here to avoid circular imports
            from .story_processor import process_stories_sequentially
            
            await process_stories_sequentially(current_user, pending_stories, socketio_server, delay_seconds=3)

        # Send final completion notification
        await send_websocket_notification(socketio_server, "background_processing_complete", {
            "task_set_id": str(task_set_id),
            "media_tasks_completed": len(pending_media_tasks),
            "stories_completed": len(pending_stories),
            "total_completed": len(pending_media_tasks) + len(pending_stories),
            "timestamp": datetime.now().isoformat()
        })

        logger.info("=" * 100)
        logger.info("🎉 BACKGROUND MEDIA PROCESSING COMPLETED")
        logger.info("=" * 100)

    except Exception as e:
        logger.error(f"❌ Error in background media processing: {e}")
        await send_websocket_notification(socketio_server, "background_processing_error", {
            "task_set_id": str(task_set_id),
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })
