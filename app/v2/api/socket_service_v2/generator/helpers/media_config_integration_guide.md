# Media Configuration Integration Guide

## Overview
The media generation configuration is now loaded directly from the database (`config` collection with `name: "media_generation"`). This guide shows how to integrate it with your existing task processing.

## Quick Start

### 1. Basic Usage
```python
from .media_config_loader import get_media_config

async def your_task_function(current_user, tasks_data):
    # Load configuration from database
    config = get_media_config(current_user=current_user)
    
    # Check if media generation is enabled
    if config.is_media_generation_enabled():
        # Process with media generation
        return await process_with_media(tasks_data, config)
    else:
        # Process without media generation
        return await process_without_media(tasks_data)
```

### 2. Task Type Checking
```python
# Check if specific task types are enabled
if config.is_task_type_enabled("single_choice"):
    # Process single choice tasks
    
if config.is_options_audio_enabled("single_choice"):
    # Generate options audio for single choice
    
if config.is_question_image_enabled("image_quiz"):
    # Generate question images for image quiz
```

### 3. Rate Limiting
```python
# Get rate limiting delays from config
options_delay = config.get_rate_limiting_delay("delay_between_options")  # 2000ms
tasks_delay = config.get_rate_limiting_delay("delay_between_tasks")      # 2000ms

# Apply delays
await asyncio.sleep(options_delay / 1000)  # Convert to seconds
```

### 4. Voice and Image Settings
```python
# Get voice settings for audio generation
voice_settings = config.get_voice_settings("single_choice", "options_audio")
# Returns: {"voice_id": "default_nepali", "speed": 1.0, "pitch": 1.0}

# Get image settings for image generation
image_settings = config.get_image_settings("image_quiz", "question_image")
# Returns: {"dimensions": {"width": 512, "height": 512}, "quality": "high", "format": "png", "style": "educational"}
```

## Integration with Existing Task Utils V2

### Update your main task processing function:

```python
# In task_utils_v2.py
from .helpers.media_config_loader import get_media_config

async def save_task_collection_and_items_with_priority_v2(
    current_user,
    session_id: str,
    tasks_data,
    collection_id=None,
    audio_storage_info=None,
    socketio_server=None,
    use_background_tasks: bool = True
):
    """V2 Task processing with database-driven configuration"""
    
    # Load media configuration from database
    config = get_media_config(current_user=current_user)
    
    logger.info("🎯 Starting V2 task processing with database configuration")
    logger.info(f"📋 Media generation enabled: {config.is_media_generation_enabled()}")
    logger.info(f"🔄 Processing order: {config.get_processing_order()}")
    
    return await process_tasks_by_type_sequential(
        current_user=current_user,
        session_id=session_id,
        tasks_data=tasks_data,
        collection_id=collection_id,
        audio_storage_info=audio_storage_info,
        socketio_server=socketio_server,
        use_background_tasks=use_background_tasks,
        config=config  # Pass config to processing functions
    )
```

### Update task type processors:

```python
# In task_type_processor.py
async def process_tasks_by_type_sequential(
    current_user,
    session_id: str,
    tasks_data,
    collection_id=None,
    audio_storage_info=None,
    socketio_server=None,
    use_background_tasks: bool = True,
    config=None  # Add config parameter
):
    """Process tasks by type with configuration"""
    
    if not config:
        config = get_media_config(current_user=current_user)
    
    # Check if media generation is globally enabled
    if not config.is_media_generation_enabled():
        logger.info("⚡ Media generation disabled - using fast processing")
        return await process_tasks_without_media(current_user, session_id, tasks_data)
    
    # Get processing order from config
    processing_order = config.get_processing_order()
    first_task_type = config.get_first_task_type()
    
    # Process according to configuration...
```

### Update individual task processors:

```python
# In single_task_processors.py
async def process_single_choice_task_complete(task_data, current_user, config=None):
    """Process single choice task with configuration"""
    
    if not config:
        config = get_media_config(current_user=current_user)
    
    # Check if this task type is enabled
    if not config.is_task_type_enabled("single_choice"):
        logger.info("⏭️ Single choice tasks disabled in config")
        return await process_task_without_media(task_data)
    
    # Check if options audio is enabled
    if config.is_options_audio_enabled("single_choice"):
        # Get voice settings
        voice_settings = config.get_voice_settings("single_choice", "options_audio")
        
        # Get rate limiting delay
        delay = config.get_rate_limiting_delay("delay_between_options")
        
        # Generate options audio with settings
        await generate_options_audio_sequential(
            task_data, 
            voice_settings, 
            delay,
            cache_enabled=config.is_caching_enabled("single_choice", "options_audio")
        )
    
    return task_data
```

## Configuration Structure in Database

The configuration document in your `config` collection should have this structure:

```json
{
  "name": "media_generation",
  "options": {
    "global_settings": {
      "enable_media_generation": true,
      "sequential_processing": true,
      "rate_limiting": {
        "delay_between_options": 2000,
        "delay_between_tasks": 2000
      }
    },
    "task_types": {
      "single_choice": {
        "enabled": true,
        "priority": 1,
        "media_generation": {
          "options_audio": {
            "enabled": true,
            "sequential": true,
            "cache_enabled": true,
            "voice_settings": {
              "voice_id": "default_nepali",
              "speed": 1.0,
              "pitch": 1.0
            }
          }
        }
      }
    },
    "processing_order": {
      "sequence": ["single_choice", "multiple_choice", "image_quiz", "audio_quiz", "story"]
    }
  }
}
```

## Available Configuration Methods

### Global Settings
- `config.is_media_generation_enabled()` - Check if media generation is globally enabled
- `config.is_caching_enabled()` - Check if caching is globally enabled
- `config.should_return_response_after_first_task()` - Check background processing setting

### Task Type Settings
- `config.is_task_type_enabled(task_type)` - Check if task type is enabled
- `config.get_task_priority(task_type)` - Get task processing priority
- `config.is_options_audio_enabled(task_type)` - Check options audio generation
- `config.is_question_image_enabled(task_type)` - Check question image generation
- `config.is_question_audio_enabled(task_type)` - Check question audio generation

### Processing Settings
- `config.get_processing_order()` - Get task type processing order
- `config.get_first_task_type()` - Get first task type to process
- `config.get_rate_limiting_delay(delay_type)` - Get rate limiting delays

### Media Settings
- `config.get_voice_settings(task_type, media_type)` - Get voice generation settings
- `config.get_image_settings(task_type, media_type)` - Get image generation settings
- `config.is_caching_enabled(task_type, media_type)` - Check media-specific caching

## Benefits

✅ **Database-driven**: Configuration loaded fresh from database each time
✅ **No file dependencies**: No need for JSON files or file system access
✅ **Tenant-specific**: Each tenant can have their own configuration
✅ **Real-time updates**: Changes in database take effect immediately
✅ **Centralized control**: All media generation settings in one place
✅ **Environment-aware**: Supports development/production overrides
✅ **Type-safe**: Proper error handling and validation

## Migration from File-based Config

If you were previously using file-based configuration:

1. ✅ **Database setup**: Configuration is already in your database
2. ✅ **Code updates**: Import paths updated to use database loader
3. ✅ **Function signatures**: All functions now require `current_user` parameter
4. ✅ **No fallbacks**: Removed JSON file fallback logic for cleaner code

The configuration system is now fully database-driven and ready to use!
