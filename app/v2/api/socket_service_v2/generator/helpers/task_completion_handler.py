"""
Task Completion Handler for Task Utils V2
Handles sequential task completion and processing.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

from .options_audio_generator import generate_options_audio
from .task_media_generator import generate_and_store_task_image, generate_and_store_task_audio, mark_task_media_failed
from .story_media_generator import generate_story_media_independent

logger = setup_new_logging(__name__)


async def complete_task_fully(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """
    Complete a single task with full processing.

    Processing order:
    1. Generate audio for ALL options (if task has options)
    2. Generate task-specific media (image/audio)

    This ensures the task is completely ready before moving to next task.
    """
    try:
        task_id = task["_id"]
        task_type = str(task["type"])
        question = task.get("question", {})

        logger.info(f"🎯 SEQUENTIAL: Completing task {task_id} ({task_type}) fully")

        # STEP 1: Generate audio for ALL options FIRST
        options = question.get("options", {})
        if options:
            logger.info(f"🔊 STEP 1: Generating audio for {len(options)} options in task {task_id}")
            await generate_options_audio(current_user, task, task_id, socketio_server)
        else:
            logger.debug(f"📝 Task {task_id} has no options, skipping options audio")

        # Add delay after options to avoid rate limits
        if options:
            logger.info(f"⏳ Waiting 2 seconds after options generation to avoid rate limits...")
            await asyncio.sleep(2)  # 2 second delay between options and task media

        # STEP 2: Generate task-specific media
        if task_type in ["image_identification", "visual_question"]:
            # Generate image for visual tasks
            keyword = question.get("answer_hint") or question.get("answer") or task.get("title", "")
            if keyword:
                logger.info(f"🖼️ STEP 2: Generating image for task {task_id} with keyword: {keyword}")
                await generate_and_store_task_image(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for image task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for image task")

        elif task_type in ["speak_word", "pronunciation", "audio_identification"]:
            # Generate audio for audio tasks
            keyword = question.get("answer_hint") or question.get("answer") or task.get("title", "")
            if keyword:
                logger.info(f"🔊 STEP 2: Generating audio for task {task_id} with keyword: {keyword}")
                await generate_and_store_task_audio(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for audio task {task_id}")
                await mark_task_media_failed(current_user, task_id, "No keyword found for audio task")

        else:
            logger.debug(f"📝 Task {task_id} ({task_type}) needs no additional media")

        # Mark task as completely ready
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._task_sequential_complete": True,
                    "metadata._all_media_ready": True,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ Task {task_id} completed with full sequential processing")

    except Exception as e:
        logger.error(f"❌ Failed to complete task sequential processing: {e}")
        raise e


async def process_remaining_tasks_sequentially(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    remaining_tasks: List[Dict[str, Any]],
    pending_stories: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """
    Process remaining tasks and stories sequentially in background.

    Processing order:
    1. Complete remaining tasks one by one (with delays)
    2. Process stories one by one (with delays)
    """
    # Add initial delay before starting background processing
    logger.info(f"⏳ Waiting 3 seconds before starting background processing to avoid rate limits...")
    await asyncio.sleep(3)

    # STEP 1: Process remaining tasks sequentially
    if remaining_tasks:
        logger.info(f"🚀 Starting SEQUENTIAL processing of {len(remaining_tasks)} remaining tasks")

        for i, task in enumerate(remaining_tasks):
            task_id = task["_id"]
            logger.info(f"📋 Processing task {i+1}/{len(remaining_tasks)}: {task_id}")

            # Complete this task fully (options + media)
            await complete_task_fully(current_user, task, socketio_server)

            # Add delay between tasks to avoid rate limits (except after last task)
            if i < len(remaining_tasks) - 1:
                logger.info(f"⏳ Waiting 3 seconds before next task to avoid rate limits...")
                await asyncio.sleep(3)

            logger.info(f"✅ Task {i+1}/{len(remaining_tasks)} completed: {task_id}")

        logger.info(f"🔄 Completed sequential processing of {len(remaining_tasks)} remaining tasks")

    # STEP 2: Process stories sequentially after all tasks
    if pending_stories:
        logger.info(f"🚀 Starting SEQUENTIAL processing of {len(pending_stories)} stories")

        for i, story in enumerate(pending_stories):
            story_id = story["_id"]
            stage = story.get("stage", 1)

            logger.info(f"📖 Processing story {i+1}/{len(pending_stories)}: {story_id} (stage {stage})")

            # Generate story image and audio
            await generate_story_media_independent(current_user, story, socketio_server)

            # Add delay between stories to avoid rate limits (except after last story)
            if i < len(pending_stories) - 1:
                logger.info(f"⏳ Waiting 3 seconds before next story to avoid rate limits...")
                await asyncio.sleep(3)

            logger.info(f"✅ Story {i+1}/{len(pending_stories)} completed: {story_id}")

        logger.info(f"🔄 Completed sequential processing of {len(pending_stories)} stories")

    total_processed = len(remaining_tasks) + len(pending_stories)
    logger.info(f"🎉 Completed ALL sequential background processing: {len(remaining_tasks)} tasks + {len(pending_stories)} stories = {total_processed} items")


async def process_media_in_background(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    pending_media_tasks: List[Dict[str, Any]],
    pending_stories: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """
    Process media tasks and story images in parallel background.

    Priority:
    1. PARALLEL: Generate media for tasks (medium priority)
    2. PARALLEL: Generate images for stories (lowest priority)
    """
    try:
        logger.info(f"🔄 Background processing started for task_set {task_set_id} with {len(pending_media_tasks)} media tasks and {len(pending_stories)} stories")

        # Log task details for debugging
        for i, task in enumerate(pending_media_tasks):
            logger.debug(f"📋 Media task {i+1}: {task.get('title', 'Unknown')} ({task.get('type', 'Unknown')})")

        for i, story in enumerate(pending_stories):
            logger.debug(f"📖 Story {i+1}: Stage {story.get('stage', 'Unknown')}")

        # PRIORITY 1: Process task media generation SEQUENTIALLY (to avoid rate limits)
        if pending_media_tasks:
            logger.info(f"🚀 Starting SEQUENTIAL processing of {len(pending_media_tasks)} task media generations")
            for i, task_item in enumerate(pending_media_tasks):
                try:
                    task_id = task_item["_id"]
                    logger.info(f"📋 Processing task {i+1}/{len(pending_media_tasks)}: {task_id}")

                    # Process each task sequentially - wait for completion before next
                    from .task_media_generator import generate_task_media_independent
                    await generate_task_media_independent(current_user, task_item, socketio_server)

                    # Add delay between tasks to avoid rate limits
                    if i < len(pending_media_tasks) - 1:  # Don't delay after last task
                        logger.info(f"⏳ Waiting 3 seconds before next task to avoid rate limits...")
                        await asyncio.sleep(3)  # 3 second delay between tasks

                    logger.info(f"✅ Task {i+1}/{len(pending_media_tasks)} completed: {task_id}")

                except Exception as task_error:
                    logger.error(f"❌ Task media generation failed for {task_item['_id']}: {task_error}")
                    continue  # Continue with next task even if one fails

        # PRIORITY 2: Process story media generation SEQUENTIALLY after tasks (lower priority)
        if pending_stories:
            logger.info(f"🚀 Starting SEQUENTIAL processing of {len(pending_stories)} story media generations")
            for i, story_item in enumerate(pending_stories):
                try:
                    story_id = story_item["_id"]
                    logger.info(f"📖 Processing story {i+1}/{len(pending_stories)}: {story_id}")

                    # Process each story sequentially - wait for completion before next
                    await generate_story_media_independent(current_user, story_item, socketio_server)

                    # Add delay between stories to avoid rate limits
                    if i < len(pending_stories) - 1:  # Don't delay after last story
                        logger.info(f"⏳ Waiting 3 seconds before next story to avoid rate limits...")
                        await asyncio.sleep(3)  # 3 second delay between stories

                    logger.info(f"✅ Story {i+1}/{len(pending_stories)} completed: {story_id}")

                except Exception as story_error:
                    logger.error(f"❌ Story media generation failed for {story_item['_id']}: {story_error}")
                    continue  # Continue with next story even if one fails

        # All processing completed sequentially
        total_processed = len(pending_media_tasks) + len(pending_stories)
        logger.info(f"🔄 Completed sequential processing of {total_processed} items ({len(pending_media_tasks)} tasks + {len(pending_stories)} stories)")

        # Update task set status to indicate all media is ready
        try:
            await current_user.async_db.task_sets.update_one(
                {"_id": task_set_id},
                {
                    "$set": {
                        "priority_processing.processing_status": "all_media_ready",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Updated task_set {task_set_id} status to all_media_ready")
        except Exception as db_error:
            logger.error(f"❌ Failed to update task_set status: {db_error}")

    except Exception as e:
        logger.error(f"❌ Background processing failed for task_set {task_set_id}: {e}")
