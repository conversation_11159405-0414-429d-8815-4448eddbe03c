"""
Sequential Processor for Task Utils V2
Handles sequential processing of tasks and stories to avoid rate limiting.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

from .task_completion_handler import complete_task_fully, process_media_in_background
from .websocket_helper import send_websocket_notification

logger = setup_new_logging(__name__)


async def process_first_task_and_return(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    first_task: Dict[str, Any],
    remaining_tasks: List[Dict[str, Any]],
    pending_stories: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Process ONLY the first task completely and return response immediately.
    Start background processing for remaining tasks and stories.

    This allows the frontend to show the first task while other tasks are being processed.
    """
    try:
        task_id = first_task["_id"]
        task_type = str(first_task["type"])

        logger.info(f"🎯 PRIORITY 1: Processing FIRST task {task_id} ({task_type}) completely")

        # Complete the first task fully (options audio + task media)
        await complete_task_fully(current_user, first_task, socketio_server)

        # Mark first task as priority complete
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._priority_complete": True,
                    "metadata._first_task": True,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ FIRST task {task_id} completed with priority processing")

        # Start background processing for remaining tasks and stories
        if remaining_tasks or pending_stories:
            logger.info(f"🚀 Starting background processing for {len(remaining_tasks)} remaining tasks and {len(pending_stories)} stories")

            # Start background processing without waiting
            asyncio.create_task(
                process_remaining_tasks_in_background(
                    current_user, task_set_id, remaining_tasks, pending_stories, socketio_server, session_id
                )
            )

        # Return response immediately after first task is complete
        return {
            "status": "first_task_ready",
            "first_task_id": str(task_id),
            "remaining_tasks": len(remaining_tasks),
            "pending_stories": len(pending_stories),
            "message": f"First task ready, {len(remaining_tasks)} tasks and {len(pending_stories)} stories processing in background"
        }

    except Exception as e:
        logger.error(f"❌ Failed to process first task {first_task.get('_id')}: {e}")
        raise e


async def process_remaining_tasks_in_background(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    remaining_tasks: List[Dict[str, Any]],
    pending_stories: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None,
    session_id: Optional[str] = None
):
    """
    Process remaining tasks and stories in background with proper delays.
    Send WebSocket notifications when complete.
    """
    try:
        # Add initial delay before starting background processing
        logger.info(f"⏳ Waiting 5 seconds before starting background processing to avoid rate limits...")
        await asyncio.sleep(5)

        total_items = len(remaining_tasks) + len(pending_stories)
        logger.info(f"🔄 Starting background processing of {total_items} items ({len(remaining_tasks)} tasks + {len(pending_stories)} stories)")

        # STEP 1: Process remaining tasks sequentially
        if remaining_tasks:
            logger.info(f"🚀 Processing {len(remaining_tasks)} remaining tasks sequentially")

            for i, task in enumerate(remaining_tasks):
                task_id = task["_id"]
                task_number = i + 1

                logger.info(f"📋 BACKGROUND TASK {task_number}/{len(remaining_tasks)}: {task_id}")

                try:
                    # Complete this task fully (options + media)
                    await complete_task_fully(current_user, task, socketio_server)

                    # Mark task as background complete
                    await current_user.async_db.task_items.update_one(
                        {"_id": task_id},
                        {
                            "$set": {
                                "metadata._background_complete": True,
                                "updated_at": datetime.now(timezone.utc)
                            }
                        }
                    )

                    logger.info(f"✅ BACKGROUND TASK {task_number}/{len(remaining_tasks)} completed: {task_id}")

                    # Send WebSocket notification for task completion
                    if socketio_server and session_id:
                        await send_websocket_notification(
                            socketio_server, session_id,
                            f"task_completed",
                            {
                                "task_id": str(task_id),
                                "task_number": task_number,
                                "total_tasks": len(remaining_tasks),
                                "status": "completed"
                            }
                        )

                    # Add delay between tasks to avoid rate limits (except after last task)
                    if i < len(remaining_tasks) - 1:
                        delay_seconds = 4  # 4 second delay between tasks
                        logger.info(f"⏳ Waiting {delay_seconds}s before next task to avoid rate limits...")
                        await asyncio.sleep(delay_seconds)

                except Exception as task_error:
                    logger.error(f"❌ Background task {task_number} failed: {task_error}")
                    # Continue with next task even if one fails
                    continue

            logger.info(f"🔄 Completed background processing of {len(remaining_tasks)} tasks")

        # STEP 2: Process stories sequentially after all tasks
        if pending_stories:
            logger.info(f"🚀 Processing {len(pending_stories)} stories sequentially")

            # Add delay before starting stories
            if remaining_tasks:  # Only delay if we processed tasks before
                logger.info(f"⏳ Waiting 3 seconds before starting stories...")
                await asyncio.sleep(3)

            for i, story in enumerate(pending_stories):
                story_id = story["_id"]
                stage = story.get("stage", 1)
                story_number = i + 1

                logger.info(f"📖 BACKGROUND STORY {story_number}/{len(pending_stories)}: {story_id} (stage {stage})")

                try:
                    # Generate story image and audio
                    from .story_media_generator import generate_story_media_independent
                    await generate_story_media_independent(current_user, story, socketio_server)

                    # Mark story as background complete
                    await current_user.async_db.story_steps.update_one(
                        {"_id": story_id},
                        {
                            "$set": {
                                "metadata._background_complete": True,
                                "updated_at": datetime.now(timezone.utc)
                            }
                        }
                    )

                    logger.info(f"✅ BACKGROUND STORY {story_number}/{len(pending_stories)} completed: {story_id}")

                    # Send WebSocket notification for story completion
                    if socketio_server and session_id:
                        await send_websocket_notification(
                            socketio_server, session_id,
                            f"story_completed",
                            {
                                "story_id": str(story_id),
                                "stage": stage,
                                "story_number": story_number,
                                "total_stories": len(pending_stories),
                                "status": "completed"
                            }
                        )

                    # Add delay between stories to avoid rate limits (except after last story)
                    if i < len(pending_stories) - 1:
                        delay_seconds = 4  # 4 second delay between stories
                        logger.info(f"⏳ Waiting {delay_seconds}s before next story to avoid rate limits...")
                        await asyncio.sleep(delay_seconds)

                except Exception as story_error:
                    logger.error(f"❌ Background story {story_number} failed: {story_error}")
                    # Continue with next story even if one fails
                    continue

            logger.info(f"🔄 Completed background processing of {len(pending_stories)} stories")

        # All background processing completed
        logger.info(f"🎉 ALL background processing completed: {len(remaining_tasks)} tasks + {len(pending_stories)} stories")

        # Update task set status to indicate all processing is complete
        try:
            await current_user.async_db.task_sets.update_one(
                {"_id": task_set_id},
                {
                    "$set": {
                        "priority_processing.processing_status": "all_complete",
                        "priority_processing.completed_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Updated task_set {task_set_id} status to all_complete")
        except Exception as db_error:
            logger.error(f"❌ Failed to update task_set status: {db_error}")

        # Send final WebSocket notification
        if socketio_server and session_id:
            await send_websocket_notification(
                socketio_server, session_id,
                "all_processing_complete",
                {
                    "task_set_id": str(task_set_id),
                    "total_tasks": len(remaining_tasks),
                    "total_stories": len(pending_stories),
                    "status": "all_complete"
                }
            )

    except Exception as e:
        logger.error(f"❌ Background processing failed for task_set {task_set_id}: {e}")

        # Send error notification
        if socketio_server and session_id:
            await send_websocket_notification(
                socketio_server, session_id,
                "processing_error",
                {
                    "task_set_id": str(task_set_id),
                    "error": str(e),
                    "status": "error"
                }
            )
