"""
WebSocket Helper for Task Utils V2
Handles WebSocket notifications and real-time updates.
"""

import json
from typing import Dict, Any, Optional
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


async def send_websocket_notification(current_user: UserTenantDB, session_id: str, 
                                    event_type: str, data: Dict[str, Any]) -> bool:
    """
    Send WebSocket notification to client.
    
    Args:
        current_user: User context with socket access
        session_id: WebSocket session ID
        event_type: Type of event (e.g., "task_completed", "progress_update")
        data: Event data to send
    
    Returns:
        True if notification sent successfully, False otherwise
    """
    try:
        # Prepare notification payload
        notification = {
            "event": event_type,
            "session_id": session_id,
            "timestamp": data.get("timestamp"),
            "data": data
        }
        
        # Send via SocketIO
        if hasattr(current_user, 'socketio') and current_user.socketio:
            await current_user.socketio.emit(event_type, notification, room=session_id)
            logger.debug(f"📡 WEBSOCKET | {event_type.upper()} | SESSION: {session_id}")
            return True
        else:
            logger.warning(f"⚠️ WEBSOCKET | NO_CONNECTION | SESSION: {session_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ WEBSOCKET ERROR | {event_type} | SESSION: {session_id} | ERROR: {e}")
        return False


async def notify_task_progress(current_user: UserTenantDB, session_id: str, 
                             task_id: str, task_number: int, total_tasks: int, 
                             status: str, details: Optional[Dict[str, Any]] = None) -> bool:
    """
    Send task progress notification.
    
    Args:
        current_user: User context
        session_id: WebSocket session ID
        task_id: Task ID
        task_number: Current task number
        total_tasks: Total number of tasks
        status: Task status (started, processing, completed, failed)
        details: Additional details about the task
    
    Returns:
        True if notification sent successfully
    """
    data = {
        "task_id": task_id,
        "task_number": task_number,
        "total_tasks": total_tasks,
        "status": status,
        "progress_percentage": round((task_number / total_tasks) * 100, 1) if total_tasks > 0 else 0,
        "details": details or {}
    }
    
    return await send_websocket_notification(current_user, session_id, "task_progress", data)


async def notify_media_generation(current_user: UserTenantDB, session_id: str,
                                task_id: str, media_type: str, status: str,
                                cached: bool = False) -> bool:
    """
    Send media generation notification.
    
    Args:
        current_user: User context
        session_id: WebSocket session ID
        task_id: Task ID
        media_type: Type of media (audio, image)
        status: Generation status (started, completed, failed)
        cached: Whether media was retrieved from cache
    
    Returns:
        True if notification sent successfully
    """
    data = {
        "task_id": task_id,
        "media_type": media_type,
        "status": status,
        "cached": cached
    }
    
    return await send_websocket_notification(current_user, session_id, "media_generation", data)


async def notify_options_progress(current_user: UserTenantDB, session_id: str,
                                task_id: str, option_key: str, total_options: int,
                                current_option: int, status: str, cached: bool = False) -> bool:
    """
    Send options audio generation progress notification.
    
    Args:
        current_user: User context
        session_id: WebSocket session ID
        task_id: Task ID
        option_key: Option key being processed
        total_options: Total number of options
        current_option: Current option number
        status: Generation status (started, completed, failed)
        cached: Whether audio was retrieved from cache
    
    Returns:
        True if notification sent successfully
    """
    data = {
        "task_id": task_id,
        "option_key": option_key,
        "total_options": total_options,
        "current_option": current_option,
        "status": status,
        "cached": cached,
        "progress_percentage": round((current_option / total_options) * 100, 1) if total_options > 0 else 0
    }
    
    return await send_websocket_notification(current_user, session_id, "options_progress", data)


async def notify_collection_complete(current_user: UserTenantDB, session_id: str,
                                   collection_id: str, summary: Dict[str, Any]) -> bool:
    """
    Send task collection completion notification.
    
    Args:
        current_user: User context
        session_id: WebSocket session ID
        collection_id: Task collection ID
        summary: Processing summary with statistics
    
    Returns:
        True if notification sent successfully
    """
    data = {
        "collection_id": collection_id,
        "summary": summary
    }
    
    return await send_websocket_notification(current_user, session_id, "collection_complete", data)


async def notify_error(current_user: UserTenantDB, session_id: str,
                      error_type: str, error_message: str, 
                      context: Optional[Dict[str, Any]] = None) -> bool:
    """
    Send error notification.
    
    Args:
        current_user: User context
        session_id: WebSocket session ID
        error_type: Type of error
        error_message: Error message
        context: Additional error context
    
    Returns:
        True if notification sent successfully
    """
    data = {
        "error_type": error_type,
        "error_message": error_message,
        "context": context or {}
    }
    
    return await send_websocket_notification(current_user, session_id, "error", data)


async def notify_cache_stats(current_user: UserTenantDB, session_id: str,
                           stats: Dict[str, Any]) -> bool:
    """
    Send cache statistics notification.
    
    Args:
        current_user: User context
        session_id: WebSocket session ID
        stats: Cache statistics
    
    Returns:
        True if notification sent successfully
    """
    data = {
        "cache_stats": stats
    }
    
    return await send_websocket_notification(current_user, session_id, "cache_stats", data)
