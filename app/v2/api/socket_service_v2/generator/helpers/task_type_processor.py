"""
Task Type Processor Helper for Task Utils V2
Organizes and processes tasks by type with sequential generation:

1. Single choice tasks - generate options audio sequentially, return response
2. Multiple choice tasks - generate options audio sequentially  
3. Image/audio tasks - generate media in background
4. Background processing for remaining tasks
"""

import asyncio
import uuid
from typing import Dict, Any, Optional

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

# Import helper functions
from .task_categorizer import categorize_tasks_by_type, get_remaining_tasks, get_first_priority_task
from .individual_task_processor import process_task_by_type, process_remaining_tasks_sequentially
from .logging_helper import create_task_logger, get_current_timestamp

logger = setup_new_logging(__name__)


async def process_tasks_by_type_sequential(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server=None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    Process tasks organized by type with sequential generation.
    
    Priority order:
    1. Single choice tasks with options audio first
    2. Multiple choice with options audio  
    3. Image/audio tasks with both media and options audio
    4. Story tasks - only return response after first task completion
    
    Args:
        current_user: Current user context
        session_id: Session identifier
        tasks_data: Tasks data from prompt maker
        collection_id: Optional collection ID
        audio_storage_info: Audio storage metadata
        socketio_server: WebSocket server for notifications
        use_background_tasks: Whether to use background processing
        
    Returns:
        Response with first task completed and background processing started
    """
    # Initialize structured logging
    start_time = get_current_timestamp()
    task_logger = create_task_logger(session_id, current_user.user.id)

    try:
        if not tasks_data or not tasks_data.get("tasks"):
            logger.error("No tasks data provided")
            return {
                "status": "error",
                "error": "No tasks data provided",
                "collection_metadata": {}
            }

        tasks = tasks_data.get("tasks", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        
        # Generate collection ID if not provided
        if not collection_id:
            collection_id = str(uuid.uuid4())

        logger.info(f"🎯 Processing {len(tasks)} tasks by type with sequential generation")

        # Organize tasks by type and priority
        task_categories = categorize_tasks_by_type(tasks)

        # Get first priority task
        first_task = get_first_priority_task(task_categories)
        first_task_completed = False

        # Process first priority task completely
        if first_task:
            try:
                logger.info(f"🎯 PRIORITY: Processing first task {first_task.get('id')} completely")
                await process_task_by_type(current_user, first_task, socketio_server)
                first_task_completed = True
                logger.info(f"✅ First task completed successfully")
            except Exception as e:
                logger.error(f"❌ Failed to complete first task: {e}")
                first_task_completed = False

        # Prepare remaining tasks for background processing
        remaining_tasks = get_remaining_tasks(task_categories, first_task)
        
        # Start background processing for remaining tasks SEQUENTIALLY
        if use_background_tasks and remaining_tasks:
            try:
                logger.info(f"🔄 Starting SEQUENTIAL background processing for {len(remaining_tasks)} remaining tasks...")

                # Create background task with proper error handling
                background_task = asyncio.create_task(process_remaining_tasks_sequentially(
                    current_user, remaining_tasks, socketio_server
                ))
                # Add callback to log any exceptions
                background_task.add_done_callback(
                    lambda task: logger.error(f"❌ Background task failed: {task.exception()}")
                    if task.exception() else logger.debug(f"✅ Background task completed successfully")
                )
                logger.info(f"🔄 Started sequential background processing")
            except Exception as e:
                logger.error(f"❌ Failed to start background processing: {e}")

        # Log structured summary
        end_time = get_current_timestamp()
        duration = (end_time - start_time).total_seconds()

        logger.info("=" * 100)
        logger.info("📊 TASK TYPE PROCESSING SUMMARY")
        logger.info("=" * 100)
        logger.info(f"⏱️  DURATION: {duration:.2f}s")
        logger.info(f"🎯 FIRST TASK: {'✅ COMPLETED' if first_task_completed else '❌ FAILED'}")
        logger.info(f"🔄 BACKGROUND TASKS: {len(remaining_tasks)}")
        logger.info(f"🆔 COLLECTION ID: {collection_id}")
        logger.info("=" * 100)

        return {
            "status": "success",
            "collection_id": collection_id,
            "first_task_completed": first_task_completed,
            "first_task": first_task if first_task_completed else None,
            "background_tasks_count": len(remaining_tasks),
            "task_categories": {k: len(v) for k, v in task_categories.items()},
            "optimization_stats": optimization_stats,
            "service_version": "v2",
            "priority_processing": True
        }

    except Exception as e:
        logger.error(f"❌ Error in task type processing: {e}")
        return {
            "status": "error",
            "error": str(e),
            "collection_metadata": {}
        }


# All helper functions have been moved to separate files:
# - categorize_tasks_by_type -> task_categorizer.py
# - get_remaining_tasks -> task_categorizer.py
# - get_first_priority_task -> task_categorizer.py
# - process_task_by_type -> individual_task_processor.py
# - process_remaining_tasks_sequentially -> individual_task_processor.py
