"""
Media Cache Helper for Task Utils V2
Provides centralized media caching with proper keyword search and cache management.
"""

import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


async def check_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, prompt_type: str) -> Optional[Dict[str, Any]]:
    """
    Check if media exists in cache for the given keyword.

    Args:
        current_user: User context with database access
        keyword: The keyword/text used to generate media
        media_type: Type of media ("audio" or "image")
        prompt_type: Type of prompt used (e.g., "audio_prompt", "imagen_prompt")

    Returns:
        Cached media data with fresh presigned URL if found, None otherwise
    """
    try:
        # Normalize keyword for search
        normalized_keyword = keyword.strip().lower()
        cache_key = hashlib.sha256(f"{normalized_keyword}_{media_type}_{prompt_type}".encode()).hexdigest()

        # Strategy 1: Exact cache key match (fastest)
        cached_media = await current_user.async_db.media.find_one({
            "cache_key": cache_key,
            "media_type": media_type,
            "prompt_type": prompt_type
        })

        # Strategy 2: Exact keyword match (case-insensitive) if cache key fails
        if not cached_media:
            cached_media = await current_user.async_db.media.find_one({
                "keyword": {"$regex": f"^{normalized_keyword}$", "$options": "i"},
                "media_type": media_type,
                "prompt_type": prompt_type
            })

        # Strategy 3: Clean keyword search (remove punctuation)
        if not cached_media:
            clean_keyword = normalized_keyword.replace("।", "").replace("?", "").replace("!", "").strip()
            if clean_keyword != normalized_keyword:
                cached_media = await current_user.async_db.media.find_one({
                    "keyword": {"$regex": f"^{clean_keyword}$", "$options": "i"},
                    "media_type": media_type,
                    "prompt_type": prompt_type
                })

        if cached_media:
            logger.info(f"🎯 CACHE HIT | {media_type.upper()} | {keyword}")

            # Generate fresh presigned URL
            try:
                presigned_url = current_user.minio.get_presigned_url(
                    bucket_name=current_user.minio_bucket_name,
                    object_name=cached_media["object_name"],
                    expires=timedelta(hours=24),
                    method="GET"
                )

                # Update file_info with fresh URL
                file_info = cached_media["file_info"].copy()
                file_info["url"] = presigned_url

                return {
                    "file_text": cached_media.get("file_text", ""),
                    "file_info": file_info,
                    "usage_metadata": cached_media.get("usage_metadata", {})
                }

            except Exception as url_error:
                logger.error(f"❌ Error generating presigned URL for cached media: {url_error}")
                # Remove invalid cache entry
                await current_user.async_db.media.delete_one({"_id": cached_media["_id"]})
                return None

        logger.debug(f"❌ CACHE MISS | {media_type.upper()} | {keyword}")
        return None

    except Exception as e:
        logger.error(f"❌ Error checking media cache for '{keyword}': {e}")
        return None


async def save_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, prompt_type: str,
                          file_text: str, file_info: Dict[str, Any], usage_metadata: Any) -> bool:
    """
    Save generated media to cache for future reuse.

    Args:
        current_user: User context with database access
        keyword: The keyword/text used to generate media
        media_type: Type of media ("audio" or "image")
        prompt_type: Type of prompt used
        file_text: Generated text content
        file_info: File information from MinIO
        usage_metadata: API usage metadata

    Returns:
        True if saved successfully, False otherwise
    """
    try:
        # Normalize keyword for consistent caching
        normalized_keyword = keyword.strip().lower()
        cache_key = hashlib.sha256(f"{normalized_keyword}_{media_type}_{prompt_type}".encode()).hexdigest()

        # Prepare cache document
        cache_document = {
            "cache_key": cache_key,
            "keyword": normalized_keyword,  # Store normalized version
            "original_keyword": keyword,    # Store original for reference
            "media_type": media_type,
            "prompt_type": prompt_type,
            "file_text": file_text,
            "file_info": file_info,
            "usage_metadata": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else usage_metadata,
            "object_name": file_info.get("object_name"),
            "folder": file_info.get("folder"),
            "content_type": file_info.get("content_type"),
            "file_size": file_info.get("file_size"),
            "created_at": datetime.now(timezone.utc),
            "user_id": current_user.user.id,
            # Add search keywords for better findability
            "search_keywords": _generate_search_keywords(keyword)
        }

        # Save to media collection (upsert to avoid duplicates)
        result = await current_user.async_db.media.update_one(
            {"cache_key": cache_key},
            {"$set": cache_document},
            upsert=True
        )

        success = result.acknowledged

        if success:
            logger.info(f"💾 CACHE SAVED | {media_type.upper()} | {keyword}")
        else:
            logger.error(f"❌ CACHE SAVE FAILED | {media_type.upper()} | {keyword}")

        return success

    except Exception as e:
        logger.error(f"❌ Error saving media to cache for '{keyword}': {e}")
        return False


def _generate_search_keywords(keyword: str) -> list:
    """Generate additional search keywords for better findability."""
    search_keywords = [keyword.strip().lower()]

    # Add variations without punctuation
    clean_keyword = keyword.replace("।", "").replace("?", "").replace("!", "").strip().lower()
    if clean_keyword not in search_keywords:
        search_keywords.append(clean_keyword)

    # Add individual words for compound keywords
    words = keyword.split()
    if len(words) > 1:
        search_keywords.extend([word.strip().lower() for word in words if word.strip()])

    return search_keywords


async def get_cache_stats(current_user: UserTenantDB) -> Dict[str, Any]:
    """Get cache statistics for monitoring."""
    try:
        total_cached = await current_user.async_db.media.count_documents({})
        audio_cached = await current_user.async_db.media.count_documents({"media_type": "audio"})
        image_cached = await current_user.async_db.media.count_documents({"media_type": "image"})

        # Get recent cache activity (last 24 hours)
        yesterday = datetime.now(timezone.utc) - timedelta(days=1)
        recent_cached = await current_user.async_db.media.count_documents({
            "created_at": {"$gte": yesterday}
        })

        return {
            "total_cached_items": total_cached,
            "audio_cached": audio_cached,
            "image_cached": image_cached,
            "recent_cached_24h": recent_cached
        }

    except Exception as e:
        logger.error(f"❌ Error getting cache stats: {e}")
        return {
            "total_cached_items": 0,
            "audio_cached": 0,
            "image_cached": 0,
            "recent_cached_24h": 0
        }


async def cleanup_old_cache(current_user: UserTenantDB, days_old: int = 30) -> int:
    """Clean up old cache entries."""
    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
        result = await current_user.async_db.media.delete_many({
            "created_at": {"$lt": cutoff_date}
        })

        deleted_count = result.deleted_count
        logger.info(f"🧹 Cleaned up {deleted_count} old cache entries (older than {days_old} days)")
        return deleted_count

    except Exception as e:
        logger.error(f"❌ Error cleaning up old cache: {e}")
        return 0
