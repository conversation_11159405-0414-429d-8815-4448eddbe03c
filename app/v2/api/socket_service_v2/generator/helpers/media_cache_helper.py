"""
Simplified Media Cache Helper for Socket Service V2
Simple media caching without complex search strategies.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import CollectionName

logger = setup_new_logging(__name__)


async def check_media_cache(current_user: UserTenantDB, keyword: str, media_type: str) -> Optional[Dict[str, Any]]:
    """Simple media cache check."""
    try:
        media_collection = current_user.db[CollectionName.MEDIA]
        cached_item = media_collection.find_one({
            "keyword": keyword.strip().lower(),
            "media_type": media_type
        })
        return cached_item.get("file_info") if cached_item else None
    except Exception as e:
        logger.warning(f"Cache check failed: {e}")
        return None


async def save_media_cache(current_user: UserTenantDB, keyword: str, media_type: str,
                          file_text: str, file_info: Dict[str, Any], usage_metadata: Dict[str, Any]):
    """Simple media cache save."""
    try:
        media_collection = current_user.db[CollectionName.MEDIA]
        media_collection.update_one(
            {"keyword": keyword.strip().lower(), "media_type": media_type},
            {
                "$set": {
                    "keyword": keyword.strip().lower(),
                    "media_type": media_type,
                    "file_text": file_text,
                    "file_info": file_info,
                    "usage_metadata": usage_metadata,
                    "created_at": datetime.now(timezone.utc)
                }
            },
            upsert=True
        )
        logger.debug(f"Cached {media_type} for keyword: {keyword}")
    except Exception as e:
        logger.warning(f"Failed to save media cache: {e}")
