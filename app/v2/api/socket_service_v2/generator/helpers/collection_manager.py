"""
Collection Manager for Task Utils V2
Handles task collection creation and management.
"""

import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId

from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

from .utility_helper import get_current_timestamp, log_performance_metrics, serialize_usage_metadata
from .logging_helper import create_task_logger
from .media_cache_helper import get_cache_stats

logger = setup_new_logging(__name__)


def map_task_type(prompt_maker_type: str) -> str:
    """
    Map prompt_maker.py task types to database QuizType enum values.
    
    Args:
        prompt_maker_type: Task type from prompt_maker.py
    
    Returns:
        Mapped QuizType enum value
    """
    type_mapping = {
        "single_choice": QuizType.SINGLE_CHOICE.value,
        "multiple_choice": QuizType.MULTIPLE_CHOICE.value,
        "image_identification": QuizType.IMAGE_IDENTIFICATION.value,
        "image_identify": QuizType.IMAGE_IDENTIFICATION.value,  # Keep old mapping for compatibility
        "speak_word": QuizType.SPEAK_WORD.value,
        "true_false": QuizType.SINGLE_CHOICE.value,  # Map true_false to single_choice
        "fill_in_blank": QuizType.ANSWER_IN_WORD.value,  # Map fill_in_blank to answer_in_word
        "visual_question": QuizType.IMAGE_IDENTIFICATION.value,  # Map visual_question to image_identification
        "pronunciation": QuizType.SPEAK_WORD.value,  # Map pronunciation to speak_word
        "audio_identification": QuizType.SPEAK_WORD.value,  # Map audio_identification to speak_word
    }
    
    return type_mapping.get(prompt_maker_type, QuizType.SINGLE_CHOICE.value)


async def create_task_collection(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create task collection with separated task types.
    
    Args:
        current_user: User context with database access
        session_id: Session identifier
        tasks_data: Output from prompt_maker_v2.py with tasks and stories
        collection_id: Optional existing collection ID
    
    Returns:
        Dictionary with collection metadata and separated tasks
    """
    try:
        # Initialize structured logging
        start_time = get_current_timestamp()
        task_logger = create_task_logger(session_id, current_user.user.id)
        
        if not tasks_data or not tasks_data.get("tasks"):
            logger.error("❌ No tasks data provided")
            return {
                "status": "error",
                "error": "No tasks data provided",
                "task_set_id": None
            }

        # Generate collection ID if not provided
        if not collection_id:
            collection_id = str(uuid.uuid4())

        tasks = tasks_data["tasks"]
        stories = tasks_data.get("stories", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        usage_metadata = serialize_usage_metadata(tasks_data.get("usage_metadata", {}))
        title = tasks_data.get("title", "Generated Task Set V2")

        # Separate tasks by priority for parallel processing
        text_tasks = []  # Highest priority - instant
        media_tasks = []  # Medium priority - parallel background

        for task in tasks:
            task_type = task.get("type", "single_choice")
            if task_type in ["single_choice", "multiple_choice", "true_false", "fill_in_blank"]:
                text_tasks.append(task)
            else:
                media_tasks.append(task)

        logger.info("=" * 100)
        logger.info("🚀 TASK COLLECTION PROCESSING STARTED")
        logger.info("=" * 100)
        logger.info(f"📊 COLLECTION: {collection_id}")
        logger.info(f"📋 TEXT TASKS (INSTANT): {len(text_tasks)}")
        logger.info(f"🎨 MEDIA TASKS (BACKGROUND): {len(media_tasks)}")
        logger.info(f"📚 STORIES (BACKGROUND): {len(stories)}")
        logger.info("-" * 100)

        # Create task set document using existing task_sets collection structure
        task_set_id = ObjectId()
        total_score = 0
        
        # Process instant text tasks
        instant_tasks = []
        for task_data in text_tasks:
            try:
                task_item_id = ObjectId()
                task_type = map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {})
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "instant_ready",
                        "_media_ready": True  # Text tasks don't need media
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                instant_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error preparing instant task: {e}")
                continue

        # Process media tasks (save structure, media generated later)
        pending_media_tasks = []
        for task_data in media_tasks:
            try:
                task_item_id = ObjectId()
                task_type = map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {})
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "media_pending",
                        "_media_ready": False
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                pending_media_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error preparing media task: {e}")
                continue

        # Process stories for background image generation
        instant_stories = []
        for story_data in stories:
            try:
                story_item_id = ObjectId()
                story_item = {
                    "_id": story_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "stage": story_data.get("stage", 1),
                    "script": story_data.get("script", ""),
                    "image": story_data.get("image", ""),
                    "metadata": {
                        **story_data.get("metadata", {}),
                        "_priority": "background_image",
                        "_image_ready": False
                    },
                    "task_title": story_data.get("task_title", ""),
                    "task_type": story_data.get("task_type", ""),
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                instant_stories.append(story_item)

            except Exception as e:
                logger.error(f"Error processing story: {e}")
                continue

        task_set_doc = {
            "_id": task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "session_id": session_id,
            "title": title,
            "input_type": InputType.AUDIO,
            "tasks": [],  # Will be populated with task IDs
            "stories": [],  # Will be populated with story IDs
            "total_tasks": len(tasks),
            "total_stories": len(stories),
            "text_tasks_ready": len(instant_tasks),
            "media_tasks_pending": len(pending_media_tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": TaskStatus.PENDING,
            "gentype": GenerationType.PRIMARY.value,
            "has_follow_up": False,
            "total_score": total_score,
            "scored": 0,
            "attempts_count": 0,
            "created_at": datetime.now(timezone.utc),
            # V2 specific metadata
            "v2_collection_id": collection_id,
            "optimization_metadata": optimization_stats,
            "usage_metadata": usage_metadata,
            "service_version": "v2",
            "priority_processing": {
                "instant_tasks": len(instant_tasks),
                "media_tasks": len(pending_media_tasks),
                "stories": len(instant_stories)
            }
        }

        return {
            "status": "success",
            "task_set_id": task_set_id,
            "task_set_doc": task_set_doc,
            "instant_tasks": instant_tasks,
            "pending_media_tasks": pending_media_tasks,
            "instant_stories": instant_stories,
            "total_score": total_score,
            "collection_id": collection_id,
            "start_time": start_time,
            "task_logger": task_logger
        }

    except Exception as e:
        logger.error(f"❌ Error creating task collection: {e}")
        return {
            "status": "error",
            "error": str(e),
            "task_set_id": None
        }


async def save_collection_to_database(
    current_user: UserTenantDB,
    collection_data: Dict[str, Any]
) -> bool:
    """
    Save the collection and all tasks/stories to database.
    
    Args:
        current_user: User context with database access
        collection_data: Collection data from create_task_collection
    
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        task_set_doc = collection_data["task_set_doc"]
        instant_tasks = collection_data["instant_tasks"]
        pending_media_tasks = collection_data["pending_media_tasks"]
        instant_stories = collection_data["instant_stories"]

        # Save instant tasks to task_items collection
        if instant_tasks:
            await current_user.async_db.task_items.insert_many(instant_tasks)
            logger.info(f"✅ Saved {len(instant_tasks)} instant text tasks to task_items collection")

        # Save media tasks to task_items collection immediately (media will be generated in background)
        if pending_media_tasks:
            await current_user.async_db.task_items.insert_many(pending_media_tasks)
            logger.info(f"✅ Saved {len(pending_media_tasks)} media tasks to task_items collection (media pending)")

        # Save instant stories to story_steps collection (images will be generated in background)
        if instant_stories:
            await current_user.async_db.story_steps.insert_many(instant_stories)
            logger.info(f"✅ Saved {len(instant_stories)} stories to story_steps collection (images pending)")

        # Update task set with references and metadata - SAME FORMAT AS V1
        all_processed_tasks = instant_tasks + pending_media_tasks
        task_set_doc["tasks"] = [str(task["_id"]) for task in all_processed_tasks]  # V1 format: simple array of task IDs
        task_set_doc["stories"] = [str(story["_id"]) for story in instant_stories]  # V1 format: simple array of story IDs

        # Save task set to existing task_sets collection
        await current_user.async_db.task_sets.insert_one(task_set_doc)
        logger.info(f"✅ Saved task set {task_set_doc['_id']} to task_sets collection")

        return True

    except Exception as e:
        logger.error(f"❌ Error saving collection to database: {e}")
        return False
