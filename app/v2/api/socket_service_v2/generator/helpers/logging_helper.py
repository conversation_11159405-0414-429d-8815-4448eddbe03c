"""
Logging Helper for Task Utils V2

Provides structured, table-like logging for better readability and tracking
of task generation, media processing, and caching operations.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union
from enum import Enum
import json

from app.shared.utils.logger import setup_new_logging

# Configure logging
logger = setup_new_logging(__name__)


class LogLevel(str, Enum):
    """Log levels for structured logging."""
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    DEBUG = "DEBUG"


class TaskLogger:
    """Structured logger for task processing with table-like output."""
    
    def __init__(self, session_id: str, user_id: str):
        self.session_id = session_id
        self.user_id = user_id
        self.task_summary = {}
        self.media_summary = {}
        self.cache_summary = {}
        
    def log_task_start(self, task_id: str, task_type: str, task_number: int, total_tasks: int):
        """Log the start of task processing."""
        self.task_summary[task_id] = {
            "task_number": task_number,
            "total_tasks": total_tasks,
            "task_type": task_type,
            "status": "STARTED",
            "options_count": 0,
            "options_cached": 0,
            "options_generated": 0,
            "options_failed": 0,
            "media_type": None,
            "media_status": "PENDING",
            "media_cached": False,
            "start_time": datetime.now(timezone.utc),
            "end_time": None,
            "duration": None
        }
        
        logger.info(f"📋 TASK {task_number}/{total_tasks} | ID: {task_id} | TYPE: {task_type} | STATUS: STARTED")
    
    def log_task_options(self, task_id: str, options_count: int):
        """Log task options processing."""
        if task_id in self.task_summary:
            self.task_summary[task_id]["options_count"] = options_count
            logger.info(f"🔊 TASK {task_id} | OPTIONS: {options_count} total")
    
    def log_option_result(self, task_id: str, option_key: str, cached: bool, success: bool):
        """Log individual option processing result."""
        if task_id in self.task_summary:
            if cached:
                self.task_summary[task_id]["options_cached"] += 1
                status = "CACHED"
            elif success:
                self.task_summary[task_id]["options_generated"] += 1
                status = "GENERATED"
            else:
                self.task_summary[task_id]["options_failed"] += 1
                status = "FAILED"
            
            logger.info(f"🎵 TASK {task_id} | OPTION {option_key}: {status}")
    
    def log_task_media(self, task_id: str, media_type: Optional[str], cached: bool, success: bool):
        """Log task media processing result."""
        if task_id in self.task_summary:
            self.task_summary[task_id]["media_type"] = media_type
            self.task_summary[task_id]["media_cached"] = cached
            
            if media_type is None:
                self.task_summary[task_id]["media_status"] = "NOT_NEEDED"
                status = "NOT_NEEDED"
            elif cached:
                self.task_summary[task_id]["media_status"] = "CACHED"
                status = "CACHED"
            elif success:
                self.task_summary[task_id]["media_status"] = "GENERATED"
                status = "GENERATED"
            else:
                self.task_summary[task_id]["media_status"] = "FAILED"
                status = "FAILED"
            
            logger.info(f"🎨 TASK {task_id} | MEDIA {media_type or 'NONE'}: {status}")
    
    def log_task_complete(self, task_id: str, success: bool):
        """Log task completion."""
        if task_id in self.task_summary:
            self.task_summary[task_id]["status"] = "COMPLETED" if success else "FAILED"
            self.task_summary[task_id]["end_time"] = datetime.now(timezone.utc)
            
            # Calculate duration
            start_time = self.task_summary[task_id]["start_time"]
            end_time = self.task_summary[task_id]["end_time"]
            duration = (end_time - start_time).total_seconds()
            self.task_summary[task_id]["duration"] = duration
            
            task_info = self.task_summary[task_id]
            task_num = task_info["task_number"]
            total = task_info["total_tasks"]
            
            logger.info(f"✅ TASK {task_num}/{total} | ID: {task_id} | STATUS: {'COMPLETED' if success else 'FAILED'} | DURATION: {duration:.1f}s")
    
    def log_cache_check(self, keyword: str, media_type: str, found: bool):
        """Log cache check result."""
        cache_key = f"{keyword}_{media_type}"
        if cache_key not in self.cache_summary:
            self.cache_summary[cache_key] = {
                "keyword": keyword,
                "media_type": media_type,
                "checks": 0,
                "hits": 0,
                "misses": 0
            }
        
        self.cache_summary[cache_key]["checks"] += 1
        if found:
            self.cache_summary[cache_key]["hits"] += 1
            logger.debug(f"🎯 CACHE HIT | {media_type.upper()} | {keyword}")
        else:
            self.cache_summary[cache_key]["misses"] += 1
            logger.debug(f"❌ CACHE MISS | {media_type.upper()} | {keyword}")
    
    def log_cache_save(self, keyword: str, media_type: str, success: bool):
        """Log cache save result."""
        cache_key = f"{keyword}_{media_type}"
        if cache_key in self.cache_summary:
            if success:
                logger.debug(f"💾 CACHE SAVED | {media_type.upper()} | {keyword}")
            else:
                logger.warning(f"❌ CACHE SAVE FAILED | {media_type.upper()} | {keyword}")
    
    def print_summary_table(self):
        """Print a comprehensive summary table of all processing."""
        logger.info("=" * 100)
        logger.info("📊 TASK PROCESSING SUMMARY")
        logger.info("=" * 100)
        
        # Task Summary Table
        if self.task_summary:
            logger.info("📋 TASKS:")
            logger.info("-" * 100)
            logger.info(f"{'#':<3} {'TASK_ID':<25} {'TYPE':<15} {'OPTIONS':<8} {'MEDIA':<12} {'STATUS':<10} {'TIME':<6}")
            logger.info("-" * 100)
            
            for task_id, info in self.task_summary.items():
                task_num = info["task_number"]
                task_type = info["task_type"][:14]
                options_info = f"{info['options_cached']}C/{info['options_generated']}G/{info['options_failed']}F"
                media_info = f"{info['media_type'] or 'NONE'}{'(C)' if info['media_cached'] else ''}"
                status = info["status"]
                duration = f"{info['duration']:.1f}s" if info['duration'] else "N/A"
                
                logger.info(f"{task_num:<3} {task_id:<25} {task_type:<15} {options_info:<8} {media_info:<12} {status:<10} {duration:<6}")
        
        # Cache Summary Table
        if self.cache_summary:
            logger.info("")
            logger.info("🎯 CACHE SUMMARY:")
            logger.info("-" * 80)
            logger.info(f"{'KEYWORD':<20} {'TYPE':<8} {'CHECKS':<7} {'HITS':<5} {'MISSES':<7} {'HIT_RATE':<8}")
            logger.info("-" * 80)
            
            for cache_key, info in self.cache_summary.items():
                keyword = info["keyword"][:19]
                media_type = info["media_type"].upper()
                checks = info["checks"]
                hits = info["hits"]
                misses = info["misses"]
                hit_rate = f"{(hits/checks*100):.1f}%" if checks > 0 else "0%"
                
                logger.info(f"{keyword:<20} {media_type:<8} {checks:<7} {hits:<5} {misses:<7} {hit_rate:<8}")
        
        # Overall Statistics
        total_tasks = len(self.task_summary)
        completed_tasks = sum(1 for info in self.task_summary.values() if info["status"] == "COMPLETED")
        total_options = sum(info["options_count"] for info in self.task_summary.values())
        cached_options = sum(info["options_cached"] for info in self.task_summary.values())
        total_cache_checks = sum(info["checks"] for info in self.cache_summary.values())
        total_cache_hits = sum(info["hits"] for info in self.cache_summary.values())
        
        logger.info("")
        logger.info("📈 OVERALL STATISTICS:")
        logger.info("-" * 50)
        logger.info(f"Tasks Completed: {completed_tasks}/{total_tasks}")
        logger.info(f"Options Processed: {total_options} ({cached_options} cached)")
        logger.info(f"Cache Hit Rate: {(total_cache_hits/total_cache_checks*100):.1f}%" if total_cache_checks > 0 else "Cache Hit Rate: 0%")
        logger.info("=" * 100)


def create_task_logger(session_id: str, user_id: str) -> TaskLogger:
    """Create a new task logger instance."""
    return TaskLogger(session_id, user_id)


def log_structured(level: LogLevel, category: str, message: str, data: Optional[Dict[str, Any]] = None):
    """Log a structured message with optional data."""
    timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] {category.upper()}: {message}"
    
    if data:
        formatted_message += f" | DATA: {json.dumps(data, default=str)}"
    
    if level == LogLevel.INFO:
        logger.info(formatted_message)
    elif level == LogLevel.SUCCESS:
        logger.info(f"✅ {formatted_message}")
    elif level == LogLevel.WARNING:
        logger.warning(f"⚠️ {formatted_message}")
    elif level == LogLevel.ERROR:
        logger.error(f"❌ {formatted_message}")
    elif level == LogLevel.DEBUG:
        logger.debug(formatted_message)


def get_current_timestamp():
    """Get current UTC timestamp."""
    return datetime.now(timezone.utc)


def log_performance_metrics(operation: str, duration: float, additional_data: Optional[Dict[str, Any]] = None):
    """Log performance metrics for operations."""
    data = {
        "operation": operation,
        "duration_seconds": round(duration, 3),
        "performance_category": "timing"
    }

    if additional_data:
        data.update(additional_data)

    log_structured(LogLevel.INFO, "PERFORMANCE", f"{operation} completed", data)
