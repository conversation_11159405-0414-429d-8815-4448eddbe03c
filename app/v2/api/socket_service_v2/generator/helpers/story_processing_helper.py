"""
Story Processing Helper for Task Utils V2
Handles story-specific media generation and processing.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio
from app.v2.api.socket_service_v2.generator.imagen import generate_image
from .media_cache_helper import check_media_cache, save_media_cache
from .websocket_helper import send_websocket_notification

logger = setup_new_logging(__name__)


async def generate_story_media(current_user: UserTenantDB, story_id: str, story_data: Dict[str, Any]) -> bool:
    """
    Generate media for story steps.
    
    Args:
        current_user: User context with database access
        story_id: Story ID
        story_data: Story data from database
    
    Returns:
        True if story media generated successfully, False otherwise
    """
    try:
        story_steps = story_data.get("story_steps", [])
        if not story_steps:
            logger.info(f"📚 STORY {story_id} | STEPS: NONE")
            return True
        
        logger.info(f"📚 STORY {story_id} | STEPS: {len(story_steps)} total")
        
        success_count = 0
        
        # Process each story step
        for i, step in enumerate(story_steps):
            step_number = i + 1
            step_success = await generate_story_step_media(current_user, story_id, step, step_number)
            
            if step_success:
                success_count += 1
            
            # Add delay between steps to avoid rate limiting
            if i < len(story_steps) - 1:
                await asyncio.sleep(2)
        
        logger.info(f"📚 STORY {story_id} | STEPS COMPLETED: {success_count}/{len(story_steps)}")
        
        return success_count > 0  # Consider success if at least one step succeeded
        
    except Exception as e:
        logger.error(f"❌ Error generating story media for {story_id}: {e}")
        return False


async def generate_story_step_media(current_user: UserTenantDB, story_id: str, 
                                  step_data: Dict[str, Any], step_number: int) -> bool:
    """
    Generate media for a single story step.
    
    Args:
        current_user: User context with database access
        story_id: Story ID
        step_data: Story step data
        step_number: Step number
    
    Returns:
        True if step media generated successfully, False otherwise
    """
    try:
        step_id = step_data.get("_id")
        step_text = step_data.get("step_text", "")
        
        if not step_text:
            logger.warning(f"⚠️ STORY {story_id} | STEP {step_number}: NO_TEXT")
            return True
        
        logger.info(f"📚 STORY {story_id} | STEP {step_number}: PROCESSING")
        
        # Generate image for story step
        image_success = await generate_story_step_image(current_user, story_id, step_id, step_text, step_number)
        
        # Generate audio for story step
        audio_success = await generate_story_step_audio(current_user, story_id, step_id, step_text, step_number)
        
        success = image_success and audio_success
        
        if success:
            logger.info(f"✅ STORY {story_id} | STEP {step_number}: COMPLETED")
        else:
            logger.error(f"❌ STORY {story_id} | STEP {step_number}: FAILED")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error generating story step media: {e}")
        return False


async def generate_story_step_image(current_user: UserTenantDB, story_id: str, step_id: str,
                                  step_text: str, step_number: int) -> bool:
    """Generate or retrieve cached image for story step."""
    try:
        # Check cache first
        cached_image = await check_media_cache(current_user, step_text, "image", "imagen_prompt")
        
        if cached_image:
            # Update story step with cached image (using correct field name)
            await current_user.async_db.story_steps.update_one(
                {"_id": ObjectId(step_id)},
                {"$set": {
                    "image_metadata": cached_image["file_info"],
                    "metadata._image_ready": True,
                    "metadata._cached": True,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )
            logger.info(f"🎨 STORY {story_id} | STEP {step_number} | IMAGE: CACHED")
            return True
        
        # Generate new image
        logger.info(f"🎨 STORY {story_id} | STEP {step_number} | IMAGE: GENERATING")
        image_result = await generate_image(current_user, step_text)
        
        if image_result and "file_info" in image_result:
            # Save to database (using correct field name)
            await current_user.async_db.story_steps.update_one(
                {"_id": ObjectId(step_id)},
                {"$set": {
                    "image_metadata": image_result["file_info"],
                    "metadata._image_ready": True,
                    "metadata._cached": False,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )
            
            # Save to cache
            await save_media_cache(
                current_user, step_text, "image", "imagen_prompt",
                image_result.get("file_text", ""),
                image_result["file_info"],
                image_result.get("usage_metadata", {})
            )
            
            logger.info(f"🎨 STORY {story_id} | STEP {step_number} | IMAGE: GENERATED")
            return True
        else:
            logger.error(f"❌ STORY {story_id} | STEP {step_number} | IMAGE: FAILED")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error generating story step image: {e}")
        return False


async def generate_story_step_audio(current_user: UserTenantDB, story_id: str, step_id: str,
                                  step_text: str, step_number: int) -> bool:
    """Generate or retrieve cached audio for story step."""
    try:
        # Check cache first
        cached_audio = await check_media_cache(current_user, step_text, "audio", "audio_prompt")
        
        if cached_audio:
            # Update story step with cached audio
            await current_user.async_db.story_steps.update_one(
                {"_id": ObjectId(step_id)},
                {"$set": {
                    "audio_file_text": cached_audio["file_text"],
                    "audio_file_info": cached_audio["file_info"],
                    "audio_usage_metadata": cached_audio["usage_metadata"]
                }}
            )
            logger.info(f"🎵 STORY {story_id} | STEP {step_number} | AUDIO: CACHED")
            return True
        
        # Generate new audio
        logger.info(f"🎵 STORY {story_id} | STEP {step_number} | AUDIO: GENERATING")
        audio_result = await generate_audio(current_user, step_text)
        
        if audio_result and "file_info" in audio_result:
            # Save to database
            await current_user.async_db.story_steps.update_one(
                {"_id": ObjectId(step_id)},
                {"$set": {
                    "audio_file_text": audio_result.get("file_text", ""),
                    "audio_file_info": audio_result["file_info"],
                    "audio_usage_metadata": audio_result.get("usage_metadata", {})
                }}
            )
            
            # Save to cache
            await save_media_cache(
                current_user, step_text, "audio", "audio_prompt",
                audio_result.get("file_text", ""),
                audio_result["file_info"],
                audio_result.get("usage_metadata", {})
            )
            
            logger.info(f"🎵 STORY {story_id} | STEP {step_number} | AUDIO: GENERATED")
            return True
        else:
            logger.error(f"❌ STORY {story_id} | STEP {step_number} | AUDIO: FAILED")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error generating story step audio: {e}")
        return False


async def process_story_collection(current_user: UserTenantDB, story_ids: List[str], session_id: str) -> Dict[str, Any]:
    """
    Process multiple stories in background.
    
    Args:
        current_user: User context with database access
        story_ids: List of story IDs to process
        session_id: WebSocket session ID
    
    Returns:
        Processing summary with success/failure counts
    """
    try:
        total_stories = len(story_ids)
        completed_stories = 0
        failed_stories = 0
        
        logger.info(f"📚 STORY PROCESSING | STORIES: {total_stories}")
        
        for i, story_id in enumerate(story_ids):
            story_number = i + 1
            
            # Get story data
            story_data = await current_user.async_db.story_steps.find_one({"story_id": story_id})
            if not story_data:
                logger.error(f"❌ STORY {story_number}/{total_stories} | ID: {story_id} | ERROR: NOT_FOUND")
                failed_stories += 1
                continue
            
            # Process story
            success = await generate_story_media(current_user, story_id, story_data)
            
            if success:
                completed_stories += 1
            else:
                failed_stories += 1
            
            # Send progress notification
            await send_websocket_notification(
                current_user, session_id, "story_progress",
                {
                    "story_id": story_id,
                    "story_number": story_number,
                    "total_stories": total_stories,
                    "success": success
                }
            )
            
            # Add delay between stories to avoid rate limiting
            if i < len(story_ids) - 1:
                await asyncio.sleep(3)
        
        summary = {
            "total_stories": total_stories,
            "completed_stories": completed_stories,
            "failed_stories": failed_stories,
            "success_rate": f"{(completed_stories/total_stories*100):.1f}%" if total_stories > 0 else "0%"
        }
        
        logger.info(f"📊 STORY PROCESSING COMPLETE | {summary}")
        
        return summary
        
    except Exception as e:
        logger.error(f"❌ Error in story collection processing: {e}")
        return {
            "total_stories": len(story_ids),
            "completed_stories": 0,
            "failed_stories": len(story_ids),
            "success_rate": "0%",
            "error": str(e)
        }
