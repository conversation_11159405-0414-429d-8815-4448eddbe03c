"""
Individual Task Processor Helper for Task Utils V2
Handles processing of individual tasks by type and background processing.
"""

import asyncio
from typing import Dict, Any
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

# Import single task processors
from .single_task_processors import (
    process_single_choice_task_complete,
    process_multiple_choice_task_complete,
    process_image_audio_task_complete,
    process_story_task_complete
)

logger = setup_new_logging(__name__)


# All individual task processing functions have been moved to single_task_processors.py


async def process_task_by_type(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server=None
):
    """
    Process a task based on its type.
    
    Args:
        current_user: Current user context
        task: Task dictionary to process
        socketio_server: WebSocket server for notifications
    """
    task_type = task.get("type", "single_choice")
    task_id = task.get("id") or task.get("_id")
    
    logger.info(f"🔄 Processing task {task_id} of type: {task_type}")
    
    try:
        if task_type == "single_choice":
            await process_single_choice_task_complete(current_user, task, socketio_server)
        elif task_type == "multiple_choice":
            await process_multiple_choice_task_complete(current_user, task, socketio_server)
        elif task_type in ["image", "audio", "image_audio"]:
            await process_image_audio_task_complete(current_user, task, socketio_server)
        elif task_type == "story":
            await process_story_task_complete(current_user, task, socketio_server)
        else:
            # Default to single choice for unknown types
            logger.warning(f"Unknown task type '{task_type}' for task {task_id}, processing as single choice")
            await process_single_choice_task_complete(current_user, task, socketio_server)
            
    except Exception as e:
        logger.error(f"❌ Failed to process task {task_id} of type {task_type}: {e}")
        raise


async def process_remaining_tasks_sequentially(
    current_user: UserTenantDB,
    remaining_tasks: list,
    socketio_server=None
):
    """
    Process remaining tasks sequentially in background.
    
    Args:
        current_user: Current user context
        remaining_tasks: List of tasks to process
        socketio_server: WebSocket server for notifications
    """
    logger.info(f"🔄 Processing {len(remaining_tasks)} remaining tasks sequentially in background")
    
    for i, task in enumerate(remaining_tasks):
        try:
            task_type = task.get("type", "single_choice")
            task_id = task.get("id") or task.get("_id")
            logger.info(f"🔄 Background task {i+1}/{len(remaining_tasks)}: {task_type} (ID: {task_id})")
            
            # Process task by type
            await process_task_by_type(current_user, task, socketio_server)
            
            # Add delay between tasks to avoid rate limiting
            if i < len(remaining_tasks) - 1:  # Don't delay after last task
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"❌ Failed to process background task {i+1}: {e}")
            continue
    
    logger.info(f"✅ Completed processing {len(remaining_tasks)} background tasks sequentially")
