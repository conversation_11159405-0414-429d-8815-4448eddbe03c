"""
Format Converter Helper for Task Utils V2
Handles conversion of tasks data to Socket.IO format.
"""

from typing import Dict, Any, List
from bson.objectid import ObjectId
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


def convert_to_socketio_format_v2(tasks_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convert V2 tasks data to Socket.IO format.
    
    This maintains compatibility with existing Socket.IO clients while
    including V2 optimization metadata.
    """
    try:
        tasks = tasks_data.get("tasks", [])
        _optimization_stats = tasks_data.get("optimization_stats", {})
        
        socketio_tasks = []
        for task in tasks:
            socketio_task = {
                "id": str(task.get("id", ObjectId())),
                "type": task.get("type", "single_choice"),
                "title": task.get("title", "Generated Task"),
                "question": task.get("question", {}),
                "total_score": task.get("total_score", 10),
                "difficulty_level": task.get("difficulty_level", 2),
                "status": task.get("status", "pending"),
                # V2 specific fields
                "_v2_optimized": task.get("_v2_optimized", False),
                "_media_excluded": task.get("_media_excluded", False)
            }
            
            # Add story if present and not optimized away
            if task.get("story") and not task.get("_media_excluded", False):
                socketio_task["story"] = task["story"]
            
            socketio_tasks.append(socketio_task)
        
        logger.info(f"Converted {len(socketio_tasks)} tasks to Socket.IO format with V2 optimizations")
        return socketio_tasks
        
    except Exception as e:
        logger.error(f"Error converting tasks to Socket.IO format: {e}")
        return []
