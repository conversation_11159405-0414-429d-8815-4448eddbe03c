"""
Simplified Followup Generator for Socket Service V2
Clean, simple followup generation without complex logic.
"""

import base64
import os
from google import genai
from google.genai import types
from bson import ObjectId
import json
from datetime import datetime, timezone
import asyncio

from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType, CollectionName
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

logger = setup_new_logging(__name__)


async def followup_generate(task_set_id: str, current_user: UserTenantDB) -> None:
    """
    SIMPLIFIED followup generation - clean and efficient.
    
    Args:
        task_set_id: The completed task set ID
        current_user: Current user context
    """
    try:
        logger.info(f"🚀 SIMPLIFIED FOLLOWUP GENERATION: Task set {task_set_id}")
        
        # Get simple config - max followup count (default 3)
        max_followup_count = _get_followup_config(current_user)
        
        # Get the completed task set
        task_set = await current_user.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"tasks": 1, "session_id": 1, "followup_count": 1, "input_content": 1, "original_task_set_id": 1}
        )

        if not task_set:
            logger.error(f"Task set {task_set_id} not found")
            return

        # Check followup limits
        current_followup_count = task_set.get("followup_count", 0)
        if current_followup_count >= max_followup_count:
            logger.info(f"🛑 Max followup depth reached ({current_followup_count}/{max_followup_count})")
            return

        # Check for existing followup at next level
        next_level = current_followup_count + 1
        original_id = task_set.get("original_task_set_id", ObjectId(task_set_id))
        
        existing = await current_user.async_db.task_sets.find_one({
            "original_task_set_id": original_id,
            "followup_count": next_level
        })
        
        if existing:
            logger.info(f"🔄 Followup level {next_level} already exists")
            return

        # Get original audio content
        audio_content = await _get_original_audio_content(current_user, task_set, task_set_id)
        if not audio_content:
            logger.error("No audio content found for followup generation")
            return

        # Get previous questions for context
        previous_questions = await _get_previous_questions(current_user, original_id, current_followup_count)

        # Generate followup tasks using simplified AI call
        followup_tasks = await _generate_followup_tasks_simple(
            current_user, audio_content, previous_questions, next_level
        )

        if not followup_tasks:
            logger.error("Failed to generate followup tasks")
            return

        # Save followup tasks to database
        await _save_followup_tasks_simple(
            current_user, followup_tasks, task_set, original_id, next_level
        )

        logger.info(f"✅ SIMPLIFIED FOLLOWUP GENERATION COMPLETED: Level {next_level}")

    except Exception as e:
        logger.error(f"❌ Simplified followup generation failed: {e}")


def _get_followup_config(current_user: UserTenantDB) -> int:
    """Get simple followup configuration."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("followup_generator_count", 3) if config_doc else 3
    except:
        return 3  # Default to 3 levels


async def _get_original_audio_content(current_user: UserTenantDB, task_set: dict, task_set_id: str) -> bytes:
    """Get original audio content for followup generation."""
    try:
        # Get input content path
        input_content = task_set.get("input_content")
        if not input_content:
            return None

        # Download audio from MinIO
        if current_user.minio:
            audio_data = current_user.minio.get_file_content(
                bucket_name=current_user.minio_bucket_name,
                object_name=input_content
            )
            return audio_data
        return None
    except Exception as e:
        logger.error(f"Failed to get audio content: {e}")
        return None


async def _get_previous_questions(current_user: UserTenantDB, original_id: ObjectId, current_level: int) -> list:
    """Get previous questions for context (simplified)."""
    try:
        questions = []
        
        # Get questions from all previous levels (0 to current_level)
        for level in range(current_level + 1):
            if level == 0:
                # Original task set
                task_set = await current_user.async_db.task_sets.find_one({"_id": original_id})
            else:
                # Followup level
                task_set = await current_user.async_db.task_sets.find_one({
                    "original_task_set_id": original_id,
                    "followup_count": level
                })
            
            if task_set and task_set.get("tasks"):
                # Get task items for this level
                task_items = await current_user.async_db.task_items.find({
                    "_id": {"$in": task_set["tasks"]}
                }).to_list(length=None)
                
                for item in task_items:
                    question = item.get("question", {})
                    if question.get("text"):
                        questions.append(question["text"])
        
        return questions
    except Exception as e:
        logger.error(f"Failed to get previous questions: {e}")
        return []


async def _generate_followup_tasks_simple(
    current_user: UserTenantDB, 
    audio_content: bytes, 
    previous_questions: list, 
    level: int
) -> list:
    """Generate followup tasks using simplified AI call."""
    try:
        # Simple prompt for followup generation
        context_text = "\n".join(previous_questions) if previous_questions else ""
        
        prompt = f"""
        Based on the audio content and previous questions, generate 4 new quiz questions for followup level {level}.
        
        Previous questions context:
        {context_text}
        
        Generate questions that build upon the previous content but are different from what was asked before.
        Return as JSON with this format:
        {{
            "tasks": [
                {{
                    "type": "single_choice",
                    "question": {{
                        "text": "Question text",
                        "options": {{"A": "Option 1", "B": "Option 2", "C": "Option 3", "D": "Option 4"}},
                        "correct_answer": "A"
                    }}
                }}
            ]
        }}
        """

        # Use Gemini to generate followup tasks
        client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        response = await client.agenerate_content(
            model="gemini-1.5-flash",
            contents=[
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(prompt),
                        types.Part.from_bytes(
                            data=audio_content,
                            mime_type="audio/wav"
                        )
                    ]
                )
            ]
        )
        
        # Parse response
        response_text = response.text.strip()
        if response_text.startswith("```json"):
            response_text = response_text[7:-3]
        elif response_text.startswith("```"):
            response_text = response_text[3:-3]
            
        result = json.loads(response_text)
        return result.get("tasks", [])
        
    except Exception as e:
        logger.error(f"Failed to generate followup tasks: {e}")
        return []


async def _save_followup_tasks_simple(
    current_user: UserTenantDB,
    tasks: list,
    parent_task_set: dict,
    original_id: ObjectId,
    level: int
):
    """Save followup tasks to database (simplified)."""
    try:
        # Create followup task set
        followup_set_id = ObjectId()
        
        followup_set_doc = {
            "_id": followup_set_id,
            "session_id": parent_task_set.get("session_id"),
            "user_id": current_user.user.id,
            "title": f"Followup Level {level}",
            "difficulty_level": "medium",
            "input_content": parent_task_set.get("input_content"),
            "gentype": GenerationType.FOLLOW_UP.value,
            "status": TaskStatus.PENDING.value,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "tasks": [],
            "followup_count": level,
            "original_task_set_id": original_id,
            "parent_task_set_id": ObjectId(parent_task_set["_id"]),
            "total_tasks": len(tasks),
            "attempted_tasks": 0,
            "total_score": len(tasks) * 10,
            "scored": 0
        }

        # Create task items
        task_items = []
        for i, task in enumerate(tasks):
            task_id = ObjectId()
            
            task_item = {
                "_id": task_id,
                "task_set_id": followup_set_id,
                "user_id": ObjectId(current_user.user.id),
                "question": task.get("question", {}),
                "correct_answer": task.get("correct_answer", {}),
                "user_answer": None,
                "type": QuizType.SINGLE_CHOICE.value,
                "input_type": InputType.AUDIO.value,
                "status": TaskStatus.PENDING.value,
                "result": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "metadata": {
                    "_priority": i,
                    "_media_ready": False,
                    "_options_audio_ready": False,
                    "image_metadata": {},
                    "audio_metadata": {}
                }
            }
            
            task_items.append(task_item)
            followup_set_doc["tasks"].append(task_id)

        # Save to database
        await current_user.async_db.task_sets.insert_one(followup_set_doc)
        await current_user.async_db.task_items.insert_many(task_items)
        
        logger.info(f"✅ Saved {len(task_items)} followup tasks for level {level}")

    except Exception as e:
        logger.error(f"Failed to save followup tasks: {e}")
