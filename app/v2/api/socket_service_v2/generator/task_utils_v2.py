"""
Task Utilities V2 for Socket Service V2

Organized task processing by type with sequential generation:
1. Single choice tasks - generate options audio sequentially, return response
2. Multiple choice tasks - generate options audio sequentially
3. Image/audio tasks - generate media in background
4. Background processing for remaining tasks
"""

# Core imports
from app.shared.utils.logger import setup_new_logging
from .helpers.task_type_processor import process_tasks_by_type_sequential
from .helpers.audio_processor import process_audio_with_prompt_maker_v2
from .helpers.format_converter import convert_to_socketio_format_v2
from .helpers.collection_processor import save_task_collection_and_items_with_priority

# Configure logging
logger = setup_new_logging(__name__)


# Main function for organized task processing by type
async def save_task_collection_and_items_with_priority_v2(
    current_user,
    session_id: str,
    tasks_data,
    collection_id=None,
    audio_storage_info=None,
    socketio_server=None,
    use_background_tasks: bool = True
):
    """
    V2 Task processing organized by type with sequential generation:

    1. Single choice tasks - generate options audio sequentially, return response
    2. Multiple choice tasks - generate options audio sequentially
    3. Image/audio tasks - generate media in background
    4. Background processing for remaining tasks
    """
    logger.info("🎯 Starting V2 organized task processing by type")

    return await process_tasks_by_type_sequential(
        current_user=current_user,
        session_id=session_id,
        tasks_data=tasks_data,
        collection_id=collection_id,
        audio_storage_info=audio_storage_info,
        socketio_server=socketio_server,
        use_background_tasks=use_background_tasks
    )


# Aliases for backward compatibility
save_task_collection_and_items = save_task_collection_and_items_with_priority
save_task_collection_and_items_v2 = save_task_collection_and_items_with_priority_v2
