"""
Routes for task item management.
"""

from fastapi import APIRouter, Depends, HTTPException, Body, Query
from typing import Dict, List, Any, Optional
from bson import ObjectId
from datetime import timedelta, datetime
import asyncio

from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.api_response import APIResponse, ResponseMetadata
from app.shared.db_enums import TaskStatus, VerificationStatus

# from app.v1.api.management_service.models.responses import APIResponse, ResponseMetadata

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()


@router.get("/{task_id}")
async def get_task_item(
    task_id: str,
    fields: Optional[List[str]] = Query(
        default=["type", "title", "question", "story","correct_answer", "user_answer", "status", "result", "attempted_tasks", "scored", "total_score", "submitted", "submitted_at"],
        description="Fields to retrieve from task"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a task item by ID.

    Args:
        task_id: The task ID
        fields: Fields to retrieve from task
        current_user: Current user information

    Returns:
        The task with selected fields
    """
    try:
        loggers.info(f"Getting task {task_id} for user {current_user.user.username}")

        # Validate task_id
        if not task_id or not ObjectId.is_valid(task_id):
            raise HTTPException(status_code=400, detail="Invalid task ID")

        # Create projection for MongoDB query
        projection = {field: 1 for field in fields}
        projection["_id"] = 1  # Always include _id

        # Get the task
        task = await current_user.async_db.task_items.find_one(
            {"_id": ObjectId(task_id)}, projection
        )

        if not task:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # Convert ObjectId to string
        task["id"] = str(task.pop("_id"))

        # Generate presigned URL for image identification tasks
        task_medias=["image_identification","image_identify","speak_word"]
        if task.get("type") in task_medias :
            try:

                    # Generate presigned URL for the image
                    presigned_url = current_user.minio.get_presigned_url(
                        bucket_name=current_user.minio_bucket_name,
                        object_name=task["question"]["metadata"]["object_name"],
                        expires=timedelta(hours=24),
                        method="GET"
                    )
                    # Add the presigned URL to the response (don't save to database)
                    task["question"]["metadata"]["url"] = presigned_url
                    loggers.debug(f"Generated presigned URL for image identification task {task_id}")
            except Exception as e:
                loggers.error(f"Error generating presigned URL for task {task_id}: {e}")
                # Continue without presigned URL
        else:
            loggers.debug(f"Task {task_id} is not an image identification task")
            task["question"]["media_url"] = None

        # Generate presigned URLs for options_metadata audio files
        if "question" in task and "options_metadata" in task["question"]:
            options_metadata = task["question"]["options_metadata"]
            if options_metadata:
                loggers.info(f"🔊 Refreshing presigned URLs for {len(options_metadata)} options in task {task_id}")

                async def refresh_option_audio_url(option_key: str, option_data: dict):
                    """Refresh presigned URL for a single option's audio."""
                    try:
                        if "file_info" in option_data and "object_name" in option_data["file_info"]:
                            object_name = option_data["file_info"]["object_name"]

                            # Generate new presigned URL
                            presigned_url = current_user.minio.get_presigned_url(
                                bucket_name=current_user.minio_bucket_name,
                                object_name=object_name,
                                expires=timedelta(hours=24),
                                method="GET"
                            )

                            # Update the audio_url with fresh presigned URL
                            option_data["audio_url"] = presigned_url
                            loggers.debug(f"✅ Refreshed audio URL for option {option_key}")

                    except Exception as e:
                        loggers.error(f"❌ Error refreshing audio URL for option {option_key}: {e}")
                        # Keep existing URL if refresh fails

                # Refresh URLs for all options in parallel
                import asyncio
                refresh_tasks = [
                    refresh_option_audio_url(option_key, option_data)
                    for option_key, option_data in options_metadata.items()
                    if isinstance(option_data, dict)
                ]

                if refresh_tasks:
                    await asyncio.gather(*refresh_tasks, return_exceptions=True)
                    loggers.debug(f"🔊 Completed presigned URL refresh for options in task {task_id}")
            else:
                loggers.debug(f"📝 Task {task_id} has empty options_metadata")
        else:
            loggers.debug(f"📝 Task {task_id} has no options_metadata")

        return task
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting task: {str(e)}")


@router.get("/set/{set_id}/tasks")
async def get_tasks_for_set(
    set_id: str,
    fields: Optional[List[str]] = Query(
        default=["type", "question", "story", "correct_answer", "status", "result", "scored", "total_score", "submitted", "complexity"],
        description="Fields to retrieve from tasks"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all tasks for a specific task set.

    Args:
        set_id: The task set ID
        fields: Fields to retrieve from tasks
        current_user: Current user information

    Returns:
        List of tasks for the specified task set
    """
    try:
        loggers.info(f"Getting tasks for task set {set_id} for user {current_user.user.username}")

        # Validate task_set_id
        if not set_id or not ObjectId.is_valid(set_id):
            raise HTTPException(status_code=400, detail="Invalid task set ID")

        # Get the task set to verify it exists and belongs to the user
        task_set = await current_user.async_db.task_sets.find_one(
            {"_id": ObjectId(set_id)},
            {"user_id": 1, "tasks": 1}
        )

        if not task_set:
            raise HTTPException(status_code=404, detail=f"Task set {set_id} not found")

        # Create projection for MongoDB query
        projection = {field: 1 for field in fields}
        projection["_id"] = 1  # Always include _id

        # Get all tasks for this task set
        tasks = []
        if "tasks" in task_set and task_set["tasks"]:
            task_ids = [ObjectId(task_id) if isinstance(task_id, str) else task_id for task_id in task_set["tasks"]]

            # Query the database for all tasks
            cursor = current_user.async_db.task_items.find(
                {"_id": {"$in": task_ids}},
                projection
            )

            # Convert cursor to list
            tasks = await cursor.to_list(length=None)

            # Convert ObjectIds to strings and refresh options audio URLs
            for task in tasks:
                task["id"] = str(task.pop("_id"))

                # Refresh presigned URLs for options_metadata audio files
                if "question" in task and "options_metadata" in task["question"]:
                    options_metadata = task["question"]["options_metadata"]
                    if options_metadata:

                        async def refresh_option_audio_url_batch(option_key: str, option_data: dict):
                            """Refresh presigned URL for a single option's audio in batch."""
                            try:
                                if "file_info" in option_data and "object_name" in option_data["file_info"]:
                                    object_name = option_data["file_info"]["object_name"]

                                    # Generate new presigned URL
                                    presigned_url = current_user.minio.get_presigned_url(
                                        bucket_name=current_user.minio_bucket_name,
                                        object_name=object_name,
                                        expires=timedelta(hours=24),
                                        method="GET"
                                    )

                                    # Update the audio_url with fresh presigned URL
                                    option_data["audio_url"] = presigned_url

                            except Exception as e:
                                loggers.error(f"❌ Error refreshing audio URL for option {option_key} in task {task['id']}: {e}")

                        # Refresh URLs for all options in this task
                        refresh_tasks = [
                            refresh_option_audio_url_batch(option_key, option_data)
                            for option_key, option_data in options_metadata.items()
                            if isinstance(option_data, dict)
                        ]

                        if refresh_tasks:
                            await asyncio.gather(*refresh_tasks, return_exceptions=True)

        loggers.info(f"🔊 Refreshed presigned URLs for options in {len(tasks)} tasks")

        # Return the tasks
        return {
            "tasks": tasks,
            "count": len(tasks),
            "task_set_id": set_id
        }
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting tasks for task set: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting tasks for task set: {str(e)}")


@router.get("/score/{task_item_id}")
async def get_task_item_score(
    task_item_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get the score for a task item.

    Args:
        task_item_id: The task item ID
        current_user: Current user information

    Returns:
        The score for the task item
    """
    try:
        loggers.info(f"Getting score for task item {task_item_id} for user {current_user.user.username}")

        # Validate task_item_id
        if not task_item_id or not ObjectId.is_valid(task_item_id):
            raise HTTPException(status_code=400, detail="Invalid task item ID")

        # Get the task item
        task_item = await current_user.async_db.task_items.find_one(
            {"_id": ObjectId(task_item_id)},
            {"scored": 1, "total_score": 1}  # Use 'total_score' consistently
        )

        if not task_item:
            raise HTTPException(status_code=404, detail=f"Task item {task_item_id} not found")

        return {
            "score": task_item["scored"],
            "total_score": task_item["total_score"]  # Use 'total_score' consistently
        }
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting task item score: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting task item score: {str(e)}")
    


# question text of task


@router.get("/question/{task_item_id}")
async def get_task_item_question(
    task_item_id: str,
    fields: Optional[List[str]] = Query(
        default=["question.text", "created_at", "type", "total_score", "scored"],
        description="Fields to retrieve from question"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get the question text for a task item.

    Args:
        task_item_id: The task item ID
        current_user: Current user information

    Returns:
        The question text for the task item
    """
    try:
        loggers.info(f"Getting question for task item {task_item_id} for user {current_user.user.username}")

        projection = {k: 1 for k in fields}
        projection["_id"] = 0

        # Validate task_item_id
        if not task_item_id or not ObjectId.is_valid(task_item_id):
            raise HTTPException(status_code=400, detail="Invalid task item ID")

        # Get the task item
        task_item = await current_user.async_db.task_items.find_one(
            {"_id": ObjectId(task_item_id)},
            projection
        )

        if not task_item:
            raise HTTPException(status_code=404, detail=f"Task item {task_item_id} not found")

        return task_item
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting task item question: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting task item question: {str(e)}")


@router.post("/retry/{task_item_id}")
async def retry_task_item(
    task_item_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Reset a single task item to allow retry - clears score and completion status.

    This endpoint:
    1. Resets the task item (score, answer, completion status)
    2. Updates the task set scores accordingly (subtracts the old score)
    3. Allows the user to attempt the task item fresh

    Args:
        task_item_id: The task item ID to retry
        current_user: Current user information

    Returns:
        Success message with reset information
    """
    try:
        loggers.info(f"Retrying task item {task_item_id} for user {current_user.user.username}")

        # Validate task_item_id
        if not task_item_id or not ObjectId.is_valid(task_item_id):
            raise HTTPException(status_code=400, detail="Invalid task item ID")

        # Get the task item to check current state - fetch all relevant fields
        task_item = await current_user.async_db.task_items.find_one(
            {"_id": ObjectId(task_item_id)},
            {
                "scored": 1, "total_score": 1, "submitted": 1, "attempts_count": 1,
                "user_answer": 1, "answered_at": 1, "is_attempted": 1, "submitted_at": 1,
                "status": 1, "result": 1, "remark": 1, "verification_status": 1,
                "verification_notes": 1, "verified_at": 1, "verified_by": 1,
                "test_status": 1, "test_results": 1, "submitted_by": 1
            }
        )

        if not task_item:
            raise HTTPException(status_code=404, detail=f"Task item {task_item_id} not found")

        # Store current state for logging and rollback if needed
        old_score = task_item.get("scored", 0)
        was_submitted = task_item.get("submitted", False)
        old_attempts = task_item.get("attempts_count", 0)
        old_status = task_item.get("status", "not_attempted")

        loggers.info(f"Resetting task {task_item_id}: score={old_score}, submitted={was_submitted}, attempts={old_attempts}, status={old_status}")

        # Comprehensive reset of ALL task item fields to pristine state
        reset_fields = {
            # Core scoring and submission fields
            "scored": 0,
            "user_answer": None,
            "answered_at": None,
            "is_attempted": False,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,

            # Status and result fields
            "status": TaskStatus.PENDING,  # Reset to initial status using proper enum
            "result": None,
            "remark": None,

            # User reference fields (clear submission tracking)
            "submitted_by": None,

            # Verification fields (reset verification state)
            "verification_status": VerificationStatus.PENDING,  # Reset to default verification status using proper enum
            "verification_notes": None,
            "verified_at": None,
            "verified_by": None,

            # Testing fields (clear any test results)
            "test_status": None,
            "test_results": None,

            # Metadata update
            "updated_at": datetime.now()
        }

        # Reset the task item with comprehensive field reset
        task_reset_result = await current_user.async_db.task_items.update_one(
            {"_id": ObjectId(task_item_id)},
            {"$set": reset_fields}
        )

        if task_reset_result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to reset task item")

        # Update the task set scores if the task was previously submitted
        if was_submitted and old_score > 0:
            # Find the task set that contains this task
            task_set = await current_user.async_db.task_sets.find_one(
                {"tasks": ObjectId(task_item_id)},
                {"_id": 1, "scored": 1, "attempted_tasks": 1}
            )

            if task_set:
                # Subtract the old score and decrease attempted tasks count
                new_scored = max(0, task_set.get("scored", 0) - old_score)
                new_attempted = max(0, task_set.get("attempted_tasks", 0) - 1)

                await current_user.async_db.task_sets.update_one(
                    {"_id": task_set["_id"]},
                    {
                        "$set": {
                            "scored": new_scored,
                            "attempted_tasks": new_attempted,
                            "updated_at": datetime.now()
                        },
                        "$pull": {"attempted_tasks_list": ObjectId(task_item_id)}
                    }
                )

                loggers.info(f"Updated task set scores: subtracted {old_score} points, new total: {new_scored}")
                loggers.info(f"Reset task {task_item_id} - ready for re-submission with improved scoring")

        return {
            "message": "Task item reset successfully - all fields cleared",
            "task_item_id": task_item_id,
            "reset_details": {
                "old_score": old_score,
                "old_attempts": old_attempts,
                "old_status": old_status,
                "was_submitted": was_submitted,
                "fields_reset": [
                    "scored", "user_answer", "answered_at", "is_attempted",
                    "submitted", "submitted_at", "attempts_count", "status",
                    "result", "remark", "submitted_by", "verification_status",
                    "verification_notes", "verified_at", "verified_by",
                    "test_status", "test_results"
                ]
            },
            "task_set_updated": was_submitted and old_score > 0,
            "reset_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error retrying task item {task_item_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrying task item: {str(e)}")


